#pragma once

#include "VirtualDesktopAPI.h"
#include <vector>
#include <string>
#include <functional>

namespace VirtualDesktop {

// Operation result structure
struct OperationResult {
    bool success;
    std::wstring message;
    int errorCode;

    OperationResult(bool s = false, const std::wstring& msg = L"", int code = 0)
        : success(s), message(msg), errorCode(code) {}
};

// Window information structure
struct WindowInfo {
    HWND handle;
    std::wstring title;
    std::wstring className;
    DWORD processId;
    GUID desktopId;
    bool isVisible;
    bool isOnCurrentDesktop;
};

// Main Virtual Desktop Manager class
class VirtualDesktopManager {
public:
    VirtualDesktopManager();
    ~VirtualDesktopManager();

    // Initialization
    OperationResult Initialize();
    void Shutdown();
    bool IsInitialized() const { return m_api.IsInitialized(); }

    // Desktop enumeration and information
    OperationResult ListDesktops(std::vector<DesktopInfo>& desktops);
    OperationResult GetCurrentDesktop(DesktopInfo& desktop);
    OperationResult GetDesktopCount(int& count);
    OperationResult GetDesktopByIndex(int index, DesktopInfo& desktop);
    OperationResult GetDesktopById(const std::wstring& guidStr, DesktopInfo& desktop);

    // Desktop management operations
    OperationResult CreateNewDesktop(DesktopInfo& newDesktop);
    OperationResult RemoveDesktop(const std::wstring& guidStr);
    OperationResult RemoveDesktopByIndex(int index);
    OperationResult SwitchToDesktop(const std::wstring& guidStr);
    OperationResult SwitchToDesktopByIndex(int index);
    OperationResult SwitchToNextDesktop();
    OperationResult SwitchToPreviousDesktop();

    // Window management operations
    OperationResult MoveWindowToDesktop(HWND hwnd, const std::wstring& desktopGuidStr);
    OperationResult MoveWindowToDesktopByIndex(HWND hwnd, int desktopIndex);
    OperationResult MoveCurrentWindowToDesktop(const std::wstring& desktopGuidStr);
    OperationResult MoveCurrentWindowToDesktopByIndex(int desktopIndex);

    // Window enumeration and information
    OperationResult GetWindowsOnDesktop(const std::wstring& desktopGuidStr, std::vector<WindowInfo>& windows);
    OperationResult GetWindowsOnCurrentDesktop(std::vector<WindowInfo>& windows);
    OperationResult GetAllWindows(std::vector<WindowInfo>& windows);
    OperationResult GetWindowInfo(HWND hwnd, WindowInfo& info);

    // Utility functions
    OperationResult IsWindowOnCurrentDesktop(HWND hwnd, bool& isOnCurrent);
    OperationResult GetWindowDesktop(HWND hwnd, DesktopInfo& desktop);
    OperationResult FindDesktopByName(const std::wstring& name, DesktopInfo& desktop);

    // Display and formatting helpers
    std::wstring FormatDesktopInfo(const DesktopInfo& desktop, bool detailed = false);
    std::wstring FormatWindowInfo(const WindowInfo& window, bool detailed = false);
    std::wstring FormatOperationResult(const OperationResult& result);

private:
    VirtualDesktopAPI m_api;

    // Helper methods
    HWND GetForegroundWindowSafe();
    std::wstring GetWindowTitle(HWND hwnd);
    std::wstring GetWindowClassName(HWND hwnd);
    DWORD GetWindowProcessId(HWND hwnd);
    bool IsWindowVisibleAndValid(HWND hwnd);
    
    // Window enumeration callback
    static BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam);
    
    struct EnumWindowsData {
        VirtualDesktopManager* manager;
        std::vector<WindowInfo>* windows;
        GUID* targetDesktopId; // nullptr for all windows
    };
};

// Utility functions for external use
namespace Utils {
    std::wstring GetLastErrorMessage(DWORD errorCode = GetLastError());
    std::wstring FormatGuid(const GUID& guid);
    bool IsValidGuidString(const std::wstring& guidStr);
    std::vector<std::wstring> SplitString(const std::wstring& str, wchar_t delimiter);
    std::wstring TrimString(const std::wstring& str);
    std::wstring ToLowerCase(const std::wstring& str);
    std::wstring ToUpperCase(const std::wstring& str);
}

} // namespace VirtualDesktop

#pragma once

#include <windows.h>
#include <wincrypt.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <chrono>
#include <mutex>

#pragma comment(lib, "crypt32.lib")
#pragma comment(lib, "advapi32.lib")

namespace EnhancedVDM {

// Security levels
enum class SecurityLevel {
    None = 0,
    Basic = 1,
    Standard = 2,
    High = 3,
    Maximum = 4
};

// Permission flags
enum class Permission : uint32_t {
    None = 0x00000000,
    ViewDesktops = 0x00000001,
    CreateDesktops = 0x00000002,
    RemoveDesktops = 0x00000004,
    SwitchDesktops = 0x00000008,
    LaunchProcesses = 0x00000010,
    TerminateProcesses = 0x00000020,
    MoveWindows = 0x00000040,
    ViewProcesses = 0x00000080,
    ManageHotkeys = 0x00000100,
    SystemControl = 0x00000200,
    AdminAccess = 0x80000000,
    FullAccess = 0xFFFFFFFF
};

// Combine permissions using bitwise OR
inline Permission operator|(Permission a, Permission b) {
    return static_cast<Permission>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

inline Permission operator&(Permission a, Permission b) {
    return static_cast<Permission>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}

// User account information
struct UserAccount {
    std::string username;
    std::string passwordHash;  // SHA-256 hash with salt
    std::string salt;
    Permission permissions;
    SecurityLevel securityLevel;
    bool enabled;
    std::chrono::system_clock::time_point lastLogin;
    std::chrono::system_clock::time_point passwordExpiry;
    int failedLoginAttempts;
    bool locked;
    
    UserAccount() : permissions(Permission::None), securityLevel(SecurityLevel::Basic), 
                   enabled(true), failedLoginAttempts(0), locked(false) {}
};

// Session information
struct SessionInfo {
    std::string sessionToken;
    std::string username;
    Permission permissions;
    std::chrono::steady_clock::time_point creationTime;
    std::chrono::steady_clock::time_point lastActivity;
    std::string clientAddress;
    std::string clientName;
    bool active;
    
    SessionInfo() : permissions(Permission::None), active(false) {}
};

// Encryption context
struct EncryptionContext {
    HCRYPTPROV cryptProvider;
    HCRYPTKEY sessionKey;
    std::vector<uint8_t> keyData;
    std::string algorithm;
    bool initialized;
    
    EncryptionContext() : cryptProvider(0), sessionKey(0), initialized(false) {}
};

// Security manager class
class SecurityManager {
public:
    SecurityManager();
    ~SecurityManager();

    // Initialization
    bool Initialize(SecurityLevel level = SecurityLevel::Standard);
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }

    // User management
    bool CreateUser(const std::string& username, const std::string& password, 
                   Permission permissions = Permission::ViewDesktops);
    bool DeleteUser(const std::string& username);
    bool UpdateUserPermissions(const std::string& username, Permission permissions);
    bool ChangeUserPassword(const std::string& username, const std::string& oldPassword, 
                           const std::string& newPassword);
    bool EnableUser(const std::string& username, bool enabled = true);
    bool IsUserEnabled(const std::string& username) const;
    std::vector<std::string> GetUserList() const;

    // Authentication
    bool AuthenticateUser(const std::string& username, const std::string& password, 
                         const std::string& clientAddress, SessionInfo& session);
    bool ValidateSession(const std::string& sessionToken, SessionInfo& session);
    bool RefreshSession(const std::string& sessionToken);
    void InvalidateSession(const std::string& sessionToken);
    void InvalidateAllSessions();

    // Authorization
    bool HasPermission(const std::string& sessionToken, Permission permission) const;
    bool HasPermission(const SessionInfo& session, Permission permission) const;
    Permission GetUserPermissions(const std::string& username) const;
    Permission GetSessionPermissions(const std::string& sessionToken) const;

    // Encryption
    bool InitializeEncryption(const std::string& sessionToken, EncryptionContext& context);
    bool EncryptData(const EncryptionContext& context, const void* plainData, size_t dataSize,
                    std::vector<uint8_t>& encryptedData);
    bool DecryptData(const EncryptionContext& context, const void* encryptedData, size_t dataSize,
                    std::vector<uint8_t>& plainData);
    void CleanupEncryption(EncryptionContext& context);

    // Security policies
    void SetPasswordPolicy(int minLength, bool requireSpecialChars, bool requireNumbers,
                          int maxAge = 0);
    void SetSessionTimeout(std::chrono::minutes timeout);
    void SetMaxFailedLogins(int maxAttempts);
    void SetAccountLockoutDuration(std::chrono::minutes duration);

    // Audit and logging
    void LogSecurityEvent(const std::string& event, const std::string& username = "",
                         const std::string& details = "");
    std::vector<std::string> GetSecurityLog(int maxEntries = 100) const;
    void ClearSecurityLog();

    // Configuration
    bool LoadConfiguration(const std::string& configFile);
    bool SaveConfiguration(const std::string& configFile) const;
    bool LoadUserDatabase(const std::string& dbFile);
    bool SaveUserDatabase(const std::string& dbFile) const;

    // Statistics
    size_t GetActiveSessionCount() const;
    size_t GetUserCount() const;
    std::chrono::steady_clock::time_point GetLastActivity() const;

private:
    bool m_initialized;
    SecurityLevel m_securityLevel;
    std::unordered_map<std::string, UserAccount> m_users;
    std::unordered_map<std::string, SessionInfo> m_sessions;
    mutable std::mutex m_usersMutex;
    mutable std::mutex m_sessionsMutex;
    
    // Security policies
    int m_minPasswordLength;
    bool m_requireSpecialChars;
    bool m_requireNumbers;
    int m_passwordMaxAge;
    std::chrono::minutes m_sessionTimeout;
    int m_maxFailedLogins;
    std::chrono::minutes m_lockoutDuration;
    
    // Audit trail
    std::vector<std::string> m_securityLog;
    mutable std::mutex m_logMutex;
    
    // Cryptographic functions
    std::string GenerateSalt() const;
    std::string HashPassword(const std::string& password, const std::string& salt) const;
    std::string GenerateSessionToken() const;
    bool ValidatePassword(const std::string& password) const;
    
    // Session management
    void CleanupExpiredSessions();
    bool IsSessionExpired(const SessionInfo& session) const;
    
    // User account helpers
    bool IsUserLocked(const std::string& username) const;
    void IncrementFailedLogins(const std::string& username);
    void ResetFailedLogins(const std::string& username);
    void LockUser(const std::string& username);
    void UnlockUser(const std::string& username);
    
    // Default users
    void CreateDefaultUsers();
};

// Security utilities
namespace SecurityUtils {
    // Cryptographic functions
    std::string GenerateRandomString(size_t length);
    std::string CalculateSHA256(const std::string& data);
    std::string CalculateSHA256(const void* data, size_t size);
    std::vector<uint8_t> GenerateRandomBytes(size_t count);
    
    // Password utilities
    bool IsPasswordStrong(const std::string& password);
    std::string GenerateRandomPassword(int length = 12);
    int CalculatePasswordStrength(const std::string& password);
    
    // Network security
    bool IsIPAddressAllowed(const std::string& address, const std::vector<std::string>& allowList);
    bool IsIPAddressBlocked(const std::string& address, const std::vector<std::string>& blockList);
    std::string GetClientFingerprint(const std::string& address, const std::string& userAgent);
    
    // System security
    bool IsRunningAsAdmin();
    bool EnablePrivilege(const std::string& privilegeName);
    bool DisablePrivilege(const std::string& privilegeName);
    std::string GetCurrentUserName();
    std::string GetComputerName();
    
    // File security
    bool SecureDeleteFile(const std::string& filename);
    bool SetFilePermissions(const std::string& filename, DWORD permissions);
    bool IsFileSecure(const std::string& filename);
    
    // Registry security
    bool SecureRegistryKey(HKEY rootKey, const std::string& subKey);
    bool DeleteRegistryValue(HKEY rootKey, const std::string& subKey, const std::string& valueName);
    
    // Memory security
    void SecureZeroMemory(void* ptr, size_t size);
    bool LockMemoryPages(void* ptr, size_t size);
    bool UnlockMemoryPages(void* ptr, size_t size);
}

} // namespace EnhancedVDM

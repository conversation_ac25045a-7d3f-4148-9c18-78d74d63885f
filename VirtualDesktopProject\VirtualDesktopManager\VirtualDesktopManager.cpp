#include "VirtualDesktopManager.h"
#include "../common/SimpleLogger.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cctype>

namespace VirtualDesktop {

VirtualDesktopManager::VirtualDesktopManager()
{
}

VirtualDesktopManager::~VirtualDesktopManager()
{
    Shutdown();
}

OperationResult VirtualDesktopManager::Initialize()
{
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Initializing Virtual Desktop Manager");

    if (m_api.IsInitialized()) {
        return OperationResult(true, L"Already initialized");
    }

    if (!m_api.Initialize()) {
        return OperationResult(false, L"Failed to initialize Virtual Desktop API", GetLastError());
    }

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Virtual Desktop Manager initialized successfully");
    return OperationResult(true, L"Virtual Desktop Manager initialized successfully");
}

void VirtualDesktopManager::Shutdown()
{
    if (m_api.IsInitialized()) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Shutting down Virtual Desktop Manager");
        m_api.Cleanup();
    }
}

OperationResult VirtualDesktopManager::ListDesktops(std::vector<DesktopInfo>& desktops)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    try {
        desktops = m_api.GetAllDesktops();
        std::wstringstream ss;
        ss << L"Retrieved " << desktops.size() << L" virtual desktops";
        return OperationResult(true, ss.str());
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while listing desktops: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::GetCurrentDesktop(DesktopInfo& desktop)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    try {
        desktop = m_api.GetCurrentDesktop();
        if (desktop.index >= 0) {
            return OperationResult(true, L"Current desktop retrieved successfully");
        } else {
            return OperationResult(false, L"Failed to get current desktop information");
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while getting current desktop: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::GetDesktopCount(int& count)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    try {
        count = m_api.GetDesktopCount();
        std::wstringstream ss;
        ss << L"Desktop count: " << count;
        return OperationResult(true, ss.str());
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while getting desktop count: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::GetDesktopByIndex(int index, DesktopInfo& desktop)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    try {
        desktop = m_api.GetDesktopByIndex(index);
        if (desktop.index >= 0) {
            std::wstringstream ss;
            ss << L"Desktop at index " << index << L" retrieved successfully";
            return OperationResult(true, ss.str());
        } else {
            std::wstringstream ss;
            ss << L"Invalid desktop index: " << index;
            return OperationResult(false, ss.str());
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while getting desktop by index: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::GetDesktopById(const std::wstring& guidStr, DesktopInfo& desktop)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    if (!Utils::IsValidGuidString(guidStr)) {
        return OperationResult(false, L"Invalid GUID format");
    }

    try {
        GUID guid = m_api.StringToGuid(guidStr);
        desktop = m_api.GetDesktopById(guid);
        if (desktop.index >= 0) {
            return OperationResult(true, L"Desktop retrieved successfully by ID");
        } else {
            return OperationResult(false, L"Desktop with specified ID not found");
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while getting desktop by ID: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::CreateNewDesktop(DesktopInfo& newDesktop)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    try {
        if (m_api.CreateDesktop(newDesktop)) {
            std::wstringstream ss;
            ss << L"New desktop created successfully with ID: " << m_api.GuidToString(newDesktop.id);
            return OperationResult(true, ss.str());
        } else {
            return OperationResult(false, L"Failed to create new desktop");
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while creating desktop: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::RemoveDesktop(const std::wstring& guidStr)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    if (!Utils::IsValidGuidString(guidStr)) {
        return OperationResult(false, L"Invalid GUID format");
    }

    try {
        GUID guid = m_api.StringToGuid(guidStr);
        if (m_api.RemoveDesktop(guid)) {
            std::wstringstream ss;
            ss << L"Desktop removed successfully: " << guidStr;
            return OperationResult(true, ss.str());
        } else {
            return OperationResult(false, L"Failed to remove desktop");
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while removing desktop: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::RemoveDesktopByIndex(int index)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    try {
        DesktopInfo desktop = m_api.GetDesktopByIndex(index);
        if (desktop.index < 0) {
            std::wstringstream ss;
            ss << L"Invalid desktop index: " << index;
            return OperationResult(false, ss.str());
        }

        if (m_api.RemoveDesktop(desktop.id)) {
            std::wstringstream ss;
            ss << L"Desktop at index " << index << L" removed successfully";
            return OperationResult(true, ss.str());
        } else {
            return OperationResult(false, L"Failed to remove desktop");
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while removing desktop by index: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::SwitchToDesktop(const std::wstring& guidStr)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    if (!Utils::IsValidGuidString(guidStr)) {
        return OperationResult(false, L"Invalid GUID format");
    }

    try {
        GUID guid = m_api.StringToGuid(guidStr);
        if (m_api.SwitchToDesktop(guid)) {
            std::wstringstream ss;
            ss << L"Switched to desktop: " << guidStr;
            return OperationResult(true, ss.str());
        } else {
            return OperationResult(false, L"Failed to switch to desktop");
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while switching to desktop: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::SwitchToDesktopByIndex(int index)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    try {
        if (m_api.SwitchToDesktopByIndex(index)) {
            std::wstringstream ss;
            ss << L"Switched to desktop at index " << index;
            return OperationResult(true, ss.str());
        } else {
            std::wstringstream ss;
            ss << L"Failed to switch to desktop at index " << index;
            return OperationResult(false, ss.str());
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while switching to desktop by index: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::MoveWindowToDesktop(HWND hwnd, const std::wstring& desktopGuidStr)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    if (!IsWindow(hwnd)) {
        return OperationResult(false, L"Invalid window handle");
    }

    if (!Utils::IsValidGuidString(desktopGuidStr)) {
        return OperationResult(false, L"Invalid GUID format");
    }

    try {
        GUID guid = m_api.StringToGuid(desktopGuidStr);
        if (m_api.MoveWindowToDesktop(hwnd, guid)) {
            std::wstringstream ss;
            ss << L"Window moved successfully to desktop: " << desktopGuidStr;
            return OperationResult(true, ss.str());
        } else {
            return OperationResult(false, L"Failed to move window to desktop");
        }
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while moving window to desktop: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::MoveWindowToDesktopByIndex(HWND hwnd, int desktopIndex)
{
    if (!m_api.IsInitialized()) {
        return OperationResult(false, L"Virtual Desktop Manager not initialized");
    }

    try {
        DesktopInfo desktop = m_api.GetDesktopByIndex(desktopIndex);
        if (desktop.index < 0) {
            std::wstringstream ss;
            ss << L"Invalid desktop index: " << desktopIndex;
            return OperationResult(false, ss.str());
        }

        std::wstring guidStr = m_api.GuidToString(desktop.id);
        return MoveWindowToDesktop(hwnd, guidStr);
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Exception while moving window to desktop by index: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        return OperationResult(false, errorMsg);
    }
}

OperationResult VirtualDesktopManager::MoveCurrentWindowToDesktop(const std::wstring& desktopGuidStr)
{
    HWND hwnd = GetForegroundWindowSafe();
    if (!hwnd || !IsWindow(hwnd)) {
        return OperationResult(false, L"No active window found");
    }

    return MoveWindowToDesktop(hwnd, desktopGuidStr);
}

OperationResult VirtualDesktopManager::MoveCurrentWindowToDesktopByIndex(int desktopIndex)
{
    HWND hwnd = GetForegroundWindowSafe();
    if (!hwnd || !IsWindow(hwnd)) {
        return OperationResult(false, L"No active window found");
    }

    return MoveWindowToDesktopByIndex(hwnd, desktopIndex);
}

// Helper methods implementation
HWND VirtualDesktopManager::GetForegroundWindowSafe()
{
    return GetForegroundWindow();
}

std::wstring VirtualDesktopManager::GetWindowTitle(HWND hwnd)
{
    if (!IsWindow(hwnd)) {
        return L"";
    }

    int length = GetWindowTextLengthW(hwnd);
    if (length == 0) {
        return L"";
    }

    std::wstring title(length + 1, L'\0');
    GetWindowTextW(hwnd, &title[0], length + 1);
    title.resize(length); // Remove null terminator
    return title;
}

std::wstring VirtualDesktopManager::GetWindowClassName(HWND hwnd)
{
    if (!IsWindow(hwnd)) {
        return L"";
    }

    wchar_t className[256] = {};
    GetClassNameW(hwnd, className, ARRAYSIZE(className));
    return std::wstring(className);
}

DWORD VirtualDesktopManager::GetWindowProcessId(HWND hwnd)
{
    if (!IsWindow(hwnd)) {
        return 0;
    }

    DWORD processId = 0;
    GetWindowThreadProcessId(hwnd, &processId);
    return processId;
}

bool VirtualDesktopManager::IsWindowVisibleAndValid(HWND hwnd)
{
    return IsWindow(hwnd) && IsWindowVisible(hwnd) && !IsIconic(hwnd);
}

// Utility functions implementation
namespace Utils {

std::wstring GetLastErrorMessage(DWORD errorCode)
{
    LPWSTR messageBuffer = nullptr;
    size_t size = FormatMessageW(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        nullptr, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPWSTR)&messageBuffer, 0, nullptr);

    std::wstring message(messageBuffer, size);
    LocalFree(messageBuffer);
    return message;
}

std::wstring FormatGuid(const GUID& guid)
{
    wchar_t guidStr[64];
    StringFromGUID2(guid, guidStr, ARRAYSIZE(guidStr));
    return std::wstring(guidStr);
}

bool IsValidGuidString(const std::wstring& guidStr)
{
    if (guidStr.empty()) {
        return false;
    }

    GUID guid;
    HRESULT hr = CLSIDFromString(guidStr.c_str(), &guid);
    return SUCCEEDED(hr);
}

std::vector<std::wstring> SplitString(const std::wstring& str, wchar_t delimiter)
{
    std::vector<std::wstring> tokens;
    std::wstringstream ss(str);
    std::wstring token;

    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(token);
    }

    return tokens;
}

std::wstring TrimString(const std::wstring& str)
{
    size_t start = str.find_first_not_of(L" \t\r\n");
    if (start == std::wstring::npos) {
        return L"";
    }

    size_t end = str.find_last_not_of(L" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::wstring ToLowerCase(const std::wstring& str)
{
    std::wstring result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::towlower);
    return result;
}

std::wstring ToUpperCase(const std::wstring& str)
{
    std::wstring result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::towupper);
    return result;
}

} // namespace Utils

} // namespace VirtualDesktop

# Virtual Desktop Manager - Usage Examples

This document provides practical examples of using the Virtual Desktop Manager application.

## Basic Operations

### 1. Getting Started

First, check if the application is working correctly:

```batch
# Show help information
VirtualDesktopManager.exe help

# Show version information
VirtualDesktopManager.exe version

# Check current desktop
VirtualDesktopManager.exe current
```

### 2. Listing Virtual Desktops

```batch
# List all virtual desktops
VirtualDesktopManager.exe list

# Alternative short form
VirtualDesktopManager.exe ls
```

**Sample Output:**
```
Virtual Desktops:
================
Index: 0 (CURRENT)
  ID: {12345678-90AB-CDEF-1234-567890ABCDEF}
  Name: Desktop 1

Index: 1
  ID: {87654321-BA09-FEDC-4321-FEDCBA098765}
  Name: Desktop 2

Index: 2
  ID: {ABCDEF12-3456-7890-ABCD-EF1234567890}
  Name: Desktop 3
```

### 3. Creating Virtual Desktops

```batch
# Create a new virtual desktop
VirtualDesktopManager.exe create

# Alternative form
VirtualDesktopManager.exe new
```

### 4. Switching Between Desktops

```batch
# Switch to desktop by index (0-based)
VirtualDesktopManager.exe switch 1

# Switch using short form
VirtualDesktopManager.exe sw 2

# Switch using GUID
VirtualDesktopManager.exe switch {87654321-BA09-FEDC-4321-FEDCBA098765}
```

### 5. Moving Windows

```batch
# Move current active window to desktop 0
VirtualDesktopManager.exe move 0

# Move current window to desktop by GUID
VirtualDesktopManager.exe move {12345678-90AB-CDEF-1234-567890ABCDEF}
```

### 6. Removing Virtual Desktops

```batch
# Remove desktop by index
VirtualDesktopManager.exe remove 2

# Remove using short forms
VirtualDesktopManager.exe rm 1
VirtualDesktopManager.exe delete 0

# Remove by GUID
VirtualDesktopManager.exe remove {ABCDEF12-3456-7890-ABCD-EF1234567890}
```

## Advanced Scenarios

### Scenario 1: Setting Up Multiple Workspaces

```batch
# Create three additional desktops for different work contexts
VirtualDesktopManager.exe create
VirtualDesktopManager.exe create
VirtualDesktopManager.exe create

# Verify all desktops were created
VirtualDesktopManager.exe count
VirtualDesktopManager.exe list
```

### Scenario 2: Organizing Windows Across Desktops

```batch
# 1. Start with current desktop (usually 0)
VirtualDesktopManager.exe current

# 2. Open your development tools (IDE, terminal, etc.)
# 3. Switch to desktop 1 for communication apps
VirtualDesktopManager.exe switch 1

# 4. Open email, chat applications
# 5. Switch to desktop 2 for documentation/research
VirtualDesktopManager.exe switch 2

# 6. Open browser, documentation tools
# 7. Move a specific window from current desktop to desktop 0
VirtualDesktopManager.exe move 0
```

### Scenario 3: Cleaning Up Unused Desktops

```batch
# List all desktops to see what's available
VirtualDesktopManager.exe list

# Remove unused desktops (be careful not to remove the last one)
VirtualDesktopManager.exe remove 3
VirtualDesktopManager.exe remove 2

# Verify the cleanup
VirtualDesktopManager.exe count
```

## Batch Scripts for Common Tasks

### Quick Desktop Setup Script

Create a file named `setup_workspaces.bat`:

```batch
@echo off
echo Setting up development workspaces...

echo Creating additional desktops...
VirtualDesktopManager.exe create
VirtualDesktopManager.exe create
VirtualDesktopManager.exe create

echo Current desktop setup:
VirtualDesktopManager.exe list

echo.
echo Workspace setup complete!
echo - Desktop 0: Main workspace
echo - Desktop 1: Development tools
echo - Desktop 2: Communication
echo - Desktop 3: Research/Documentation
pause
```

### Desktop Navigation Script

Create a file named `navigate_desktops.bat`:

```batch
@echo off
:menu
cls
echo ================================
echo Virtual Desktop Navigator
echo ================================
echo.
echo Current desktop:
VirtualDesktopManager.exe current
echo.
echo Available desktops:
VirtualDesktopManager.exe list
echo.
echo 1. Switch to Desktop 0
echo 2. Switch to Desktop 1
echo 3. Switch to Desktop 2
echo 4. Switch to Desktop 3
echo 5. Create New Desktop
echo 6. Exit
echo.
set /p choice=Enter your choice (1-6): 

if "%choice%"=="1" VirtualDesktopManager.exe switch 0
if "%choice%"=="2" VirtualDesktopManager.exe switch 1
if "%choice%"=="3" VirtualDesktopManager.exe switch 2
if "%choice%"=="4" VirtualDesktopManager.exe switch 3
if "%choice%"=="5" VirtualDesktopManager.exe create
if "%choice%"=="6" exit /b 0

if not "%choice%"=="6" (
    echo.
    pause
    goto menu
)
```

## Integration with Other Tools

### PowerShell Integration

```powershell
# PowerShell function to wrap the Virtual Desktop Manager
function Switch-VirtualDesktop {
    param(
        [Parameter(Mandatory=$true)]
        [int]$Index
    )
    
    & "VirtualDesktopManager.exe" switch $Index
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully switched to desktop $Index" -ForegroundColor Green
    } else {
        Write-Host "Failed to switch to desktop $Index" -ForegroundColor Red
    }
}

# Usage
Switch-VirtualDesktop -Index 1
```

### Task Scheduler Integration

You can create scheduled tasks to automatically set up your workspace:

1. Open Task Scheduler
2. Create a new task
3. Set trigger (e.g., at logon)
4. Set action to run your setup script

## Troubleshooting Common Issues

### Issue: "Failed to initialize Virtual Desktop API"

**Solution:**
```batch
# Check Windows version
winver

# Verify Virtual Desktops are enabled
# Go to Settings > System > Multitasking > Virtual desktops

# Try running as administrator
# Right-click Command Prompt > Run as administrator
VirtualDesktopManager.exe list
```

### Issue: "Desktop with specified ID not found"

**Solution:**
```batch
# Refresh the desktop list
VirtualDesktopManager.exe list

# Use the correct index (0-based)
VirtualDesktopManager.exe switch 0

# Verify desktop exists before operations
VirtualDesktopManager.exe count
```

### Issue: "Cannot remove desktop: only one desktop remaining"

**Solution:**
```batch
# Create additional desktops first
VirtualDesktopManager.exe create
VirtualDesktopManager.exe create

# Then remove the unwanted desktop
VirtualDesktopManager.exe remove 0
```

## Performance Tips

1. **Use indices instead of GUIDs** when possible for faster operations
2. **Batch operations** in scripts rather than individual commands
3. **Check desktop count** before removal operations
4. **Use logging** to debug issues (check `virtual_desktop_manager.log`)

## Best Practices

1. **Always verify operations** with `list` or `current` commands
2. **Create backup desktops** before removing existing ones
3. **Use meaningful naming** in your scripts and documentation
4. **Test scripts** in a safe environment before production use
5. **Monitor log files** for any issues or warnings

---

For more information, see the main README file or run `VirtualDesktopManager.exe help`.

# Virtual Desktop Manager

A modern C++20 implementation for managing Windows 10/11 Virtual Desktops using native Windows APIs. This project provides a standalone command-line tool that leverages the Windows Virtual Desktop system without relying on hidden desktop mechanisms.

## Features

- **List Virtual Desktops**: Enumerate all available virtual desktops with detailed information
- **Create/Remove Desktops**: Dynamically create new virtual desktops or remove existing ones
- **Switch Between Desktops**: Programmatically switch to any virtual desktop by index or GUID
- **Window Management**: Move windows between virtual desktops
- **Modern C++20**: Built with modern C++ standards and best practices
- **Comprehensive Logging**: Detailed logging system for debugging and monitoring
- **Error Handling**: Robust error handling with graceful failure recovery
- **Unicode Support**: Full Unicode support for international characters

## System Requirements

- **Operating System**: Windows 10 version 1803 (build 17134) or later, Windows 11 (all versions)
- **Windows SDK**: 10.0.19041.0 or later
- **Visual Studio**: 2019 or later with C++20 support
- **Architecture**: x86 and x64 supported

## Project Structure

```
VirtualDesktopManager/
├── VirtualDesktopManager.sln          # Visual Studio solution file
├── VirtualDesktopManager/
│   ├── VirtualDesktopManager.vcxproj   # Project file
│   ├── VirtualDesktopAPI.h             # Core API wrapper header
│   ├── VirtualDesktopAPI.cpp           # Core API implementation
│   ├── VirtualDesktopManager.h         # High-level manager header
│   ├── VirtualDesktopManager.cpp       # High-level manager implementation
│   └── main.cpp                        # Command-line interface
├── common/
│   ├── SimpleLogger.h                  # Logging system header
│   └── SimpleLogger.cpp                # Logging system implementation
└── README_VirtualDesktopManager.md     # This file
```

## Building the Project

### Prerequisites

1. Install Visual Studio 2019 or later with:
   - C++ desktop development workload
   - Windows 10/11 SDK (10.0.19041.0 or later)
   - MSVC v143 compiler toolset

### Build Steps

1. **Clone or extract the project**
2. **Open the solution**:
   ```
   VirtualDesktopManager.sln
   ```
3. **Select build configuration**:
   - Debug or Release
   - x86 or x64 (x64 recommended)
4. **Build the solution**:
   - Press `Ctrl+Shift+B` or use Build → Build Solution

### Command Line Build

```batch
# Using MSBuild
msbuild VirtualDesktopManager.sln /p:Configuration=Release /p:Platform=x64

# Using Visual Studio Developer Command Prompt
devenv VirtualDesktopManager.sln /build "Release|x64"
```

## Usage

### Command Line Interface

The application provides a comprehensive command-line interface:

```
VirtualDesktopManager.exe <command> [arguments]
```

### Available Commands

| Command | Aliases | Description | Example |
|---------|---------|-------------|---------|
| `list` | `ls` | List all virtual desktops | `VirtualDesktopManager.exe list` |
| `current` | `cur` | Show current virtual desktop | `VirtualDesktopManager.exe current` |
| `count` | - | Show total number of desktops | `VirtualDesktopManager.exe count` |
| `create` | `new` | Create a new virtual desktop | `VirtualDesktopManager.exe create` |
| `remove <id>` | `rm <id>`, `delete <id>` | Remove desktop by index or GUID | `VirtualDesktopManager.exe remove 2` |
| `switch <id>` | `sw <id>` | Switch to desktop by index or GUID | `VirtualDesktopManager.exe switch 1` |
| `move <id>` | - | Move current window to desktop | `VirtualDesktopManager.exe move 0` |
| `help` | `-h`, `--help` | Show help message | `VirtualDesktopManager.exe help` |
| `version` | `-v` | Show version information | `VirtualDesktopManager.exe version` |

### Usage Examples

```batch
# List all virtual desktops
VirtualDesktopManager.exe list

# Create a new virtual desktop
VirtualDesktopManager.exe create

# Switch to desktop at index 1
VirtualDesktopManager.exe switch 1

# Move current window to desktop at index 0
VirtualDesktopManager.exe move 0

# Remove desktop at index 2
VirtualDesktopManager.exe remove 2

# Switch using GUID
VirtualDesktopManager.exe switch {12345678-90AB-CDEF-1234-567890ABCDEF}
```

### Sample Output

```
Virtual Desktops:
================
Index: 0 (CURRENT)
  ID: {12345678-90AB-CDEF-1234-567890ABCDEF}
  Name: Desktop 1

Index: 1
  ID: {87654321-BA09-FEDC-4321-FEDCBA098765}
  Name: Desktop 2
```

## API Reference

### Core Classes

#### `VirtualDesktopAPI`
Low-level wrapper around Windows Virtual Desktop APIs.

**Key Methods:**
- `Initialize()` - Initialize COM and Virtual Desktop interfaces
- `GetAllDesktops()` - Retrieve all virtual desktops
- `CreateDesktop()` - Create a new virtual desktop
- `RemoveDesktop()` - Remove a virtual desktop
- `SwitchToDesktop()` - Switch to a specific desktop
- `MoveWindowToDesktop()` - Move a window between desktops

#### `VirtualDesktopManager`
High-level manager providing user-friendly operations.

**Key Methods:**
- `ListDesktops()` - Get formatted desktop list
- `CreateNewDesktop()` - Create desktop with validation
- `SwitchToDesktopByIndex()` - Switch using index
- `MoveCurrentWindow()` - Move active window

### Data Structures

#### `DesktopInfo`
```cpp
struct DesktopInfo {
    GUID id;                    // Unique desktop identifier
    std::wstring name;          // Desktop name
    std::wstring wallpaperPath; // Wallpaper file path
    bool isCurrent;             // Is this the current desktop?
    int index;                  // Desktop index (0-based)
};
```

#### `OperationResult`
```cpp
struct OperationResult {
    bool success;               // Operation success status
    std::wstring message;       // Result message
    int errorCode;              // Error code (if applicable)
};
```

## Logging

The application uses a comprehensive logging system that writes to `virtual_desktop_manager.log`:

- **Info**: General operation information
- **Warning**: Non-critical issues
- **Error**: Operation failures
- **Debug**: Detailed debugging information

Log files are automatically rotated and include timestamps for all entries.

## Error Handling

The application implements multiple layers of error handling:

1. **COM Error Handling**: Proper HRESULT checking for all COM operations
2. **Exception Safety**: Try-catch blocks around all major operations
3. **Input Validation**: Comprehensive validation of user inputs
4. **Graceful Degradation**: Fallback behavior when advanced features are unavailable

## Limitations and Known Issues

1. **Windows Version Dependency**: Some features require specific Windows 10/11 builds
2. **Undocumented APIs**: Uses some undocumented Windows APIs that may change
3. **Elevation Requirements**: Some operations may require administrator privileges
4. **Single Desktop Limitation**: Cannot remove the last remaining desktop
5. **COM Threading**: Must be run from the main UI thread for optimal compatibility

## Troubleshooting

### Common Issues

**"Failed to initialize Virtual Desktop API"**
- Ensure you're running Windows 10 1803+ or Windows 11
- Try running as administrator
- Check that Virtual Desktops are enabled in Windows settings

**"Desktop with specified ID not found"**
- Verify the desktop index or GUID is correct
- Use `list` command to see available desktops
- Desktop may have been removed by another application

**"Cannot remove desktop: only one desktop remaining"**
- Windows requires at least one virtual desktop
- Create additional desktops before removing the current one

### Debug Mode

Build in Debug configuration for additional logging and error information:

```batch
msbuild VirtualDesktopManager.sln /p:Configuration=Debug /p:Platform=x64
```

## Contributing

This project follows modern C++ best practices:

- **C++20 Standard**: Uses modern C++ features and idioms
- **RAII**: Proper resource management
- **Exception Safety**: Strong exception safety guarantees
- **Unicode**: Full Unicode support throughout
- **Const Correctness**: Proper const usage
- **Smart Pointers**: Automatic memory management where applicable

## License

This project is provided as-is for educational and development purposes. Please ensure compliance with Microsoft's terms of service when using Windows APIs.

## Version History

- **v1.0.0**: Initial release with core virtual desktop management functionality

---

For more information about Windows Virtual Desktop APIs, see the [Microsoft Documentation](https://docs.microsoft.com/en-us/windows/win32/api/shobjidl_core/nn-shobjidl_core-ivirtualdesktopmanager).

#include <windows.h>
#include <commctrl.h>
#include <iostream>
#include <memory>
#include "ServerApplication.h"
#include "MainWindow.h"
#include "SimpleLogger.h"

#pragma comment(lib, "comctl32.lib")
#pragma comment(linker,"\"/manifestdependency:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' processorArchitecture='*' publicKeyToken='6595b64144ccf1df' language='*'\"")

using namespace EnhancedVDM;

// Global application instance
ServerApplication* g_pServerApp = nullptr;

// Application entry point
int WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
{
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);

    // Initialize COM
    HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
    if (FAILED(hr)) {
        MessageBox(nullptr, L"Failed to initialize COM", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // Initialize common controls
    INITCOMMONCONTROLSEX icex = {};
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES | ICC_LISTVIEW_CLASSES | ICC_TAB_CLASSES | 
                 ICC_TREEVIEW_CLASSES | ICC_BAR_CLASSES | ICC_PROGRESS_CLASS;
    
    if (!InitCommonControlsEx(&icex)) {
        MessageBox(nullptr, L"Failed to initialize common controls", L"Error", MB_OK | MB_ICONERROR);
        CoUninitialize();
        return 1;
    }

    int exitCode = 0;
    
    try {
        // Create and initialize the server application
        auto serverApp = std::make_unique<ServerApplication>();
        g_pServerApp = serverApp.get();

        if (!serverApp->Initialize(hInstance)) {
            MessageBox(nullptr, L"Failed to initialize server application", L"Error", MB_OK | MB_ICONERROR);
            exitCode = 1;
        } else {
            // Run the application
            exitCode = serverApp->Run();
        }

        // Cleanup
        serverApp->Shutdown();
        g_pServerApp = nullptr;
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"Unhandled exception: ";
        errorMsg += std::wstring(e.what(), e.what() + strlen(e.what()));
        MessageBox(nullptr, errorMsg.c_str(), L"Fatal Error", MB_OK | MB_ICONERROR);
        exitCode = 1;
    }
    catch (...) {
        MessageBox(nullptr, L"Unknown exception occurred", L"Fatal Error", MB_OK | MB_ICONERROR);
        exitCode = 1;
    }

    // Cleanup COM
    CoUninitialize();
    
    return exitCode;
}

// Console entry point for debugging
int main(int argc, char* argv[])
{
    // Check if we should run in console mode
    bool consoleMode = false;
    for (int i = 1; i < argc; ++i) {
        if (strcmp(argv[i], "--console") == 0 || strcmp(argv[i], "-c") == 0) {
            consoleMode = true;
            break;
        }
    }

    if (consoleMode) {
        // Allocate a console for this GUI application
        if (AllocConsole()) {
            // Redirect stdout, stdin, stderr to console
            freopen_s(reinterpret_cast<FILE**>(stdout), "CONOUT$", "w", stdout);
            freopen_s(reinterpret_cast<FILE**>(stderr), "CONOUT$", "w", stderr);
            freopen_s(reinterpret_cast<FILE**>(stdin), "CONIN$", "r", stdin);
            
            // Make cout, wcout, cin, wcin, wcerr, cerr, wclog and clog
            // point to console as well
            std::ios::sync_with_stdio(true);
            
            std::wcout << L"Enhanced Virtual Desktop Manager Server - Console Mode\n";
            std::wcout << L"=====================================================\n\n";
        }

        // Initialize logging to console
        SimpleLogger::Initialize("server_console.log");
        SimpleLogger::Log(LogLevel::Info, "Server starting in console mode");

        try {
            // Create and initialize the server application
            auto serverApp = std::make_unique<ServerApplication>();
            g_pServerApp = serverApp.get();

            if (!serverApp->Initialize(GetModuleHandle(nullptr))) {
                std::wcout << L"Error: Failed to initialize server application\n";
                return 1;
            }

            std::wcout << L"Server initialized successfully\n";
            std::wcout << L"Starting server...\n";

            if (!serverApp->StartServer()) {
                std::wcout << L"Error: Failed to start server\n";
                return 1;
            }

            std::wcout << L"Server started successfully\n";
            std::wcout << L"Press 'q' and Enter to quit, 's' for statistics, 'c' for client list\n\n";

            // Simple console interface
            char input;
            bool running = true;
            while (running && std::cin >> input) {
                switch (input) {
                case 'q':
                case 'Q':
                    running = false;
                    break;
                case 's':
                case 'S':
                    {
                        const auto& stats = serverApp->GetStatistics();
                        std::wcout << L"\nServer Statistics:\n";
                        std::wcout << L"  Current connections: " << stats.currentConnections << L"\n";
                        std::wcout << L"  Total connections: " << stats.totalConnections << L"\n";
                        std::wcout << L"  Commands processed: " << stats.totalCommandsProcessed << L"\n";
                        std::wcout << L"  Bytes received: " << stats.totalBytesReceived << L"\n";
                        std::wcout << L"  Bytes sent: " << stats.totalBytesSent << L"\n\n";
                    }
                    break;
                case 'c':
                case 'C':
                    {
                        auto clients = serverApp->GetConnectedClients();
                        std::wcout << L"\nConnected Clients (" << clients.size() << L"):\n";
                        for (const auto& client : clients) {
                            std::wcout << L"  " << std::wstring(client->clientName.begin(), client->clientName.end())
                                      << L" (" << std::wstring(client->address.begin(), client->address.end()) << L")\n";
                        }
                        std::wcout << L"\n";
                    }
                    break;
                default:
                    std::wcout << L"Commands: q=quit, s=statistics, c=clients\n";
                    break;
                }
            }

            std::wcout << L"Shutting down server...\n";
            serverApp->StopServer();
            serverApp->Shutdown();
            std::wcout << L"Server shutdown complete\n";

            g_pServerApp = nullptr;
        }
        catch (const std::exception& e) {
            std::wcout << L"Error: " << std::wstring(e.what(), e.what() + strlen(e.what())) << L"\n";
            return 1;
        }

        SimpleLogger::Shutdown();
        
        if (AllocConsole()) {
            FreeConsole();
        }
        
        return 0;
    }
    else {
        // Run in GUI mode
        return wWinMain(GetModuleHandle(nullptr), nullptr, GetCommandLineW(), SW_SHOW);
    }
}

#include "VirtualDesktopManager.h"
#include "../common/SimpleLogger.h"
#include <iostream>
#include <iomanip>
#include <string>
#include <vector>
#include <map>
#include <functional>
#include <algorithm>
#include <io.h>
#include <fcntl.h>

using namespace VirtualDesktop;

class VirtualDesktopCLI {
public:
    VirtualDesktopCLI() : m_manager() {}

    int Run(int argc, wchar_t* argv[]) {
        // Initialize logging
        ModernHVNC::SimpleLogger::Initialize("virtual_desktop_manager.log");
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Virtual Desktop Manager CLI starting");

        // Initialize the manager
        auto result = m_manager.Initialize();
        if (!result.success) {
            std::wcout << L"Error: Failed to initialize Virtual Desktop Manager\n";
            std::wcout << L"Reason: " << result.message << L"\n";
            return 1;
        }

        // Parse command line arguments
        if (argc < 2) {
            ShowHelp();
            return 0;
        }

        std::wstring command = argv[1];
        std::transform(command.begin(), command.end(), command.begin(), ::towlower);

        // Execute command
        int exitCode = ExecuteCommand(command, argc - 2, argv + 2);

        // Cleanup
        m_manager.Shutdown();
        ModernHVNC::SimpleLogger::Shutdown();
        
        return exitCode;
    }

private:
    VirtualDesktopManager m_manager;

    int ExecuteCommand(const std::wstring& command, int argc, wchar_t* argv[]) {
        if (command == L"list" || command == L"ls") {
            return ListDesktops();
        }
        else if (command == L"current" || command == L"cur") {
            return ShowCurrentDesktop();
        }
        else if (command == L"count") {
            return ShowDesktopCount();
        }
        else if (command == L"create" || command == L"new") {
            return CreateDesktop();
        }
        else if (command == L"remove" || command == L"rm" || command == L"delete") {
            if (argc < 1) {
                std::wcout << L"Error: Desktop index or GUID required for remove command\n";
                return 1;
            }
            return RemoveDesktop(argv[0]);
        }
        else if (command == L"switch" || command == L"sw") {
            if (argc < 1) {
                std::wcout << L"Error: Desktop index or GUID required for switch command\n";
                return 1;
            }
            return SwitchDesktop(argv[0]);
        }
        else if (command == L"move") {
            if (argc < 1) {
                std::wcout << L"Error: Desktop index or GUID required for move command\n";
                return 1;
            }
            return MoveCurrentWindow(argv[0]);
        }
        else if (command == L"help" || command == L"-h" || command == L"--help") {
            ShowHelp();
            return 0;
        }
        else if (command == L"version" || command == L"-v" || command == L"--version") {
            ShowVersion();
            return 0;
        }
        else {
            std::wcout << L"Error: Unknown command '" << command << L"'\n";
            std::wcout << L"Use 'help' to see available commands.\n";
            return 1;
        }
    }

    int ListDesktops() {
        std::vector<DesktopInfo> desktops;
        auto result = m_manager.ListDesktops(desktops);
        
        if (!result.success) {
            std::wcout << L"Error: " << result.message << L"\n";
            return 1;
        }

        if (desktops.empty()) {
            std::wcout << L"No virtual desktops found.\n";
            return 0;
        }

        std::wcout << L"\nVirtual Desktops:\n";
        std::wcout << L"================\n";
        
        for (const auto& desktop : desktops) {
            std::wcout << L"Index: " << desktop.index;
            if (desktop.isCurrent) {
                std::wcout << L" (CURRENT)";
            }
            std::wcout << L"\n";
            
            std::wcout << L"  ID: " << Utils::FormatGuid(desktop.id) << L"\n";
            
            if (!desktop.name.empty()) {
                std::wcout << L"  Name: " << desktop.name << L"\n";
            }
            
            if (!desktop.wallpaperPath.empty()) {
                std::wcout << L"  Wallpaper: " << desktop.wallpaperPath << L"\n";
            }
            
            std::wcout << L"\n";
        }

        return 0;
    }

    int ShowCurrentDesktop() {
        DesktopInfo desktop;
        auto result = m_manager.GetCurrentDesktop(desktop);
        
        if (!result.success) {
            std::wcout << L"Error: " << result.message << L"\n";
            return 1;
        }

        std::wcout << L"\nCurrent Virtual Desktop:\n";
        std::wcout << L"=======================\n";
        std::wcout << L"Index: " << desktop.index << L"\n";
        std::wcout << L"ID: " << Utils::FormatGuid(desktop.id) << L"\n";
        
        if (!desktop.name.empty()) {
            std::wcout << L"Name: " << desktop.name << L"\n";
        }
        
        if (!desktop.wallpaperPath.empty()) {
            std::wcout << L"Wallpaper: " << desktop.wallpaperPath << L"\n";
        }

        return 0;
    }

    int ShowDesktopCount() {
        int count;
        auto result = m_manager.GetDesktopCount(count);
        
        if (!result.success) {
            std::wcout << L"Error: " << result.message << L"\n";
            return 1;
        }

        std::wcout << L"Total virtual desktops: " << count << L"\n";
        return 0;
    }

    int CreateDesktop() {
        DesktopInfo newDesktop;
        auto result = m_manager.CreateNewDesktop(newDesktop);
        
        if (!result.success) {
            std::wcout << L"Error: " << result.message << L"\n";
            return 1;
        }

        std::wcout << L"Successfully created new virtual desktop:\n";
        std::wcout << L"Index: " << newDesktop.index << L"\n";
        std::wcout << L"ID: " << Utils::FormatGuid(newDesktop.id) << L"\n";
        
        return 0;
    }

    int RemoveDesktop(const std::wstring& target) {
        OperationResult result;
        
        // Try to parse as index first
        try {
            int index = std::stoi(target);
            result = m_manager.RemoveDesktopByIndex(index);
        }
        catch (...) {
            // Try as GUID
            result = m_manager.RemoveDesktop(target);
        }
        
        if (!result.success) {
            std::wcout << L"Error: " << result.message << L"\n";
            return 1;
        }

        std::wcout << L"Success: " << result.message << L"\n";
        return 0;
    }

    int SwitchDesktop(const std::wstring& target) {
        OperationResult result;
        
        // Try to parse as index first
        try {
            int index = std::stoi(target);
            result = m_manager.SwitchToDesktopByIndex(index);
        }
        catch (...) {
            // Try as GUID
            result = m_manager.SwitchToDesktop(target);
        }
        
        if (!result.success) {
            std::wcout << L"Error: " << result.message << L"\n";
            return 1;
        }

        std::wcout << L"Success: " << result.message << L"\n";
        return 0;
    }

    int MoveCurrentWindow(const std::wstring& target) {
        // Get current foreground window
        HWND hwnd = GetForegroundWindow();
        if (!hwnd || !IsWindow(hwnd)) {
            std::wcout << L"Error: No active window found\n";
            return 1;
        }

        // Get window title for user feedback
        int titleLength = GetWindowTextLengthW(hwnd);
        std::wstring windowTitle;
        if (titleLength > 0) {
            windowTitle.resize(titleLength + 1);
            GetWindowTextW(hwnd, &windowTitle[0], titleLength + 1);
            windowTitle.resize(titleLength);
        }

        OperationResult result;
        
        // Try to parse as index first
        try {
            int index = std::stoi(target);
            // Get desktop GUID by index
            DesktopInfo desktop;
            auto getResult = m_manager.GetDesktopByIndex(index, desktop);
            if (!getResult.success) {
                std::wcout << L"Error: " << getResult.message << L"\n";
                return 1;
            }
            
            std::wstring guidStr = Utils::FormatGuid(desktop.id);
            result = m_manager.MoveWindowToDesktop(hwnd, guidStr);
        }
        catch (...) {
            // Try as GUID
            result = m_manager.MoveWindowToDesktop(hwnd, target);
        }
        
        if (!result.success) {
            std::wcout << L"Error: " << result.message << L"\n";
            return 1;
        }

        std::wcout << L"Successfully moved window";
        if (!windowTitle.empty()) {
            std::wcout << L" '" << windowTitle << L"'";
        }
        std::wcout << L" to the specified desktop\n";
        
        return 0;
    }

    void ShowHelp() {
        std::wcout << L"\nVirtual Desktop Manager - Windows 10/11 Virtual Desktop Control\n";
        std::wcout << L"===============================================================\n\n";

        std::wcout << L"USAGE:\n";
        std::wcout << L"  VirtualDesktopManager.exe <command> [arguments]\n\n";

        std::wcout << L"COMMANDS:\n";
        std::wcout << L"  list, ls              List all virtual desktops\n";
        std::wcout << L"  current, cur          Show current virtual desktop\n";
        std::wcout << L"  count                 Show total number of virtual desktops\n";
        std::wcout << L"  create, new           Create a new virtual desktop\n";
        std::wcout << L"  remove <id>, rm <id>  Remove a virtual desktop by index or GUID\n";
        std::wcout << L"  switch <id>, sw <id>  Switch to a virtual desktop by index or GUID\n";
        std::wcout << L"  move <id>             Move current window to desktop by index or GUID\n";
        std::wcout << L"  help, -h, --help      Show this help message\n";
        std::wcout << L"  version, -v           Show version information\n\n";

        std::wcout << L"ARGUMENTS:\n";
        std::wcout << L"  <id>                  Desktop index (0, 1, 2, ...) or GUID\n";
        std::wcout << L"                        Example: 0 or {12345678-90AB-CDEF-1234-567890ABCDEF}\n\n";

        std::wcout << L"EXAMPLES:\n";
        std::wcout << L"  VirtualDesktopManager.exe list\n";
        std::wcout << L"  VirtualDesktopManager.exe create\n";
        std::wcout << L"  VirtualDesktopManager.exe switch 1\n";
        std::wcout << L"  VirtualDesktopManager.exe move 0\n";
        std::wcout << L"  VirtualDesktopManager.exe remove 2\n\n";

        std::wcout << L"NOTES:\n";
        std::wcout << L"  - Requires Windows 10 version 1803 or later\n";
        std::wcout << L"  - Some operations require elevated privileges\n";
        std::wcout << L"  - Desktop indices start from 0\n";
        std::wcout << L"  - Cannot remove the last remaining desktop\n\n";
    }

    void ShowVersion() {
        std::wcout << L"\nVirtual Desktop Manager v1.0.0\n";
        std::wcout << L"Built for Windows 10/11 Virtual Desktop APIs\n";
        std::wcout << L"Copyright (c) 2025 - Modern C++20 Implementation\n\n";

        std::wcout << L"Features:\n";
        std::wcout << L"  - List and manage virtual desktops\n";
        std::wcout << L"  - Create and remove virtual desktops\n";
        std::wcout << L"  - Switch between virtual desktops\n";
        std::wcout << L"  - Move windows between virtual desktops\n";
        std::wcout << L"  - Comprehensive error handling and logging\n\n";

        std::wcout << L"System Requirements:\n";
        std::wcout << L"  - Windows 10 version 1803 (build 17134) or later\n";
        std::wcout << L"  - Windows 11 (all versions)\n";
        std::wcout << L"  - Windows SDK 10.0.19041.0 or later\n\n";
    }
};

int wmain(int argc, wchar_t* argv[]) {
    // Set console to handle Unicode properly
    _setmode(_fileno(stdout), _O_U16TEXT);
    _setmode(_fileno(stderr), _O_U16TEXT);
    _setmode(_fileno(stdin), _O_U16TEXT);

    try {
        VirtualDesktopCLI cli;
        return cli.Run(argc, argv);
    }
    catch (const std::exception& e) {
        std::wcout << L"Fatal error: ";
        std::wcout << std::wstring(e.what(), e.what() + strlen(e.what())) << L"\n";
        return 1;
    }
    catch (...) {
        std::wcout << L"Fatal error: Unknown exception occurred\n";
        return 1;
    }
}

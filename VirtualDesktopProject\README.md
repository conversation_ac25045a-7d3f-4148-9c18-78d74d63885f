# Virtual Desktop Manager

A modern C++20 implementation for managing Windows 10/11 Virtual Desktops using native Windows APIs. This project provides a standalone command-line tool that leverages the Windows Virtual Desktop system without relying on hidden desktop mechanisms.

## Features

- **List Virtual Desktops**: Enumerate all available virtual desktops with detailed information
- **Create/Remove Desktops**: Dynamically create new virtual desktops or remove existing ones
- **Switch Between Desktops**: Programmatically switch to any virtual desktop by index or GUID
- **Window Management**: Move windows between virtual desktops
- **Modern C++20**: Built with modern C++ standards and best practices
- **Comprehensive Logging**: Detailed logging system for debugging and monitoring
- **Error Handling**: Robust error handling with graceful failure recovery
- **Unicode Support**: Full Unicode support for international characters

## System Requirements

- **Operating System**: Windows 10 version 1803 (build 17134) or later, Windows 11 (all versions)
- **Windows SDK**: 10.0.19041.0 or later
- **Visual Studio**: 2019 or later with C++20 support
- **Architecture**: x86 and x64 supported

## Quick Start

### Building the Project

1. **Prerequisites**: Install Visual Studio 2019+ with C++ desktop development workload
2. **Open Developer Command Prompt** for Visual Studio
3. **Navigate to project directory**:
   ```batch
   cd VirtualDesktopProject
   ```
4. **Build the project**:
   ```batch
   build.bat
   ```

### Basic Usage

```batch
# List all virtual desktops
VirtualDesktopManager.exe list

# Create a new virtual desktop
VirtualDesktopManager.exe create

# Switch to desktop at index 1
VirtualDesktopManager.exe switch 1

# Move current window to desktop at index 0
VirtualDesktopManager.exe move 0

# Show help
VirtualDesktopManager.exe help
```

## Project Structure

```
VirtualDesktopProject/
├── VirtualDesktopManager.sln          # Visual Studio solution file
├── VirtualDesktopManager/
│   ├── VirtualDesktopManager.vcxproj   # Project file
│   ├── VirtualDesktopAPI.h             # Core API wrapper header
│   ├── VirtualDesktopAPI.cpp           # Core API implementation
│   ├── VirtualDesktopManager.h         # High-level manager header
│   ├── VirtualDesktopManager.cpp       # High-level manager implementation
│   └── main.cpp                        # Command-line interface
├── common/
│   ├── SimpleLogger.h                  # Logging system header
│   └── SimpleLogger.cpp                # Logging system implementation
├── build.bat                           # Build script
└── README.md                           # This file
```

## Available Commands

| Command | Aliases | Description | Example |
|---------|---------|-------------|---------|
| `list` | `ls` | List all virtual desktops | `VirtualDesktopManager.exe list` |
| `current` | `cur` | Show current virtual desktop | `VirtualDesktopManager.exe current` |
| `count` | - | Show total number of desktops | `VirtualDesktopManager.exe count` |
| `create` | `new` | Create a new virtual desktop | `VirtualDesktopManager.exe create` |
| `remove <id>` | `rm <id>`, `delete <id>` | Remove desktop by index or GUID | `VirtualDesktopManager.exe remove 2` |
| `switch <id>` | `sw <id>` | Switch to desktop by index or GUID | `VirtualDesktopManager.exe switch 1` |
| `move <id>` | - | Move current window to desktop | `VirtualDesktopManager.exe move 0` |
| `help` | `-h`, `--help` | Show help message | `VirtualDesktopManager.exe help` |
| `version` | `-v` | Show version information | `VirtualDesktopManager.exe version` |

## Build Options

```batch
# Build in Release mode (default)
build.bat

# Build in Debug mode
build.bat debug

# Build for x86 platform
build.bat x86

# Clean and rebuild
build.bat clean release x64
```

## API Reference

### Core Classes

#### `VirtualDesktopAPI`
Low-level wrapper around Windows Virtual Desktop APIs.

#### `VirtualDesktopManager`
High-level manager providing user-friendly operations.

### Key Data Structures

```cpp
struct DesktopInfo {
    GUID id;                    // Unique desktop identifier
    std::wstring name;          // Desktop name
    std::wstring wallpaperPath; // Wallpaper file path
    bool isCurrent;             // Is this the current desktop?
    int index;                  // Desktop index (0-based)
};

struct OperationResult {
    bool success;               // Operation success status
    std::wstring message;       // Result message
    int errorCode;              // Error code (if applicable)
};
```

## Logging

The application creates detailed logs in `virtual_desktop_manager.log` with:
- **Info**: General operation information
- **Warning**: Non-critical issues
- **Error**: Operation failures
- **Debug**: Detailed debugging information

## Troubleshooting

### Common Issues

**"Failed to initialize Virtual Desktop API"**
- Ensure you're running Windows 10 1803+ or Windows 11
- Try running as administrator
- Check that Virtual Desktops are enabled in Windows settings

**"Desktop with specified ID not found"**
- Verify the desktop index or GUID is correct
- Use `list` command to see available desktops

**Build Errors**
- Ensure Windows SDK 10.0.19041.0+ is installed
- Verify Visual Studio 2019+ with C++ workload
- Run from Visual Studio Developer Command Prompt

## Limitations

1. **Windows Version Dependency**: Requires Windows 10 1803+ or Windows 11
2. **Undocumented APIs**: Uses some undocumented Windows APIs that may change
3. **Single Desktop Limitation**: Cannot remove the last remaining desktop
4. **COM Threading**: Must be run from appropriate COM apartment

## License

This project is provided as-is for educational and development purposes.

## Version History

- **v1.0.0**: Initial release with core virtual desktop management functionality

---

For detailed usage examples, see the help command: `VirtualDesktopManager.exe help`

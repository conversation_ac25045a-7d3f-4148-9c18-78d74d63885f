#pragma once

#include <windows.h>
#include <shobjidl_core.h>
#include <comdef.h>
#include <winstring.h>
#include <roapi.h>
#include <vector>
#include <string>
#include <memory>
#include <functional>

namespace EnhancedVDM {

// Forward declarations
struct IVirtualDesktop;
struct IVirtualDesktopManagerInternal;
struct IVirtualDesktopNotificationService;
struct IVirtualDesktopNotification;

// GUIDs for undocumented interfaces (Windows 10/11)
// These may change between Windows versions - we'll try multiple versions
struct VirtualDesktopGUIDs {
    GUID CLSID_VirtualDesktopManagerInternal;
    GUID IID_IVirtualDesktopManagerInternal;
    GUID IID_IVirtualDesktop;
};

// Windows 11 22H2+ GUIDs
static const VirtualDesktopGUIDs WIN11_22H2_GUIDS = {
    { 0xC5E0CDCA, 0x7B6E, 0x41B2, { 0x9F, 0xC4, 0xD9, 0x39, 0x75, 0xCC, 0x46, 0x7B } },
    { 0xB2F925B9, 0x5A0F, 0x4D2E, { 0x9F, 0x4D, 0x2B, 0x1C, 0xA9, 0x40, 0xE7, 0xDE } },
    { 0x536D3495, 0xB208, 0x4CC9, { 0xAE, 0x26, 0xDE, 0x8111, 0x27, 0x5B, 0xF6, 0x96 } }
};

// Windows 10/11 Legacy GUIDs (fallback)
static const VirtualDesktopGUIDs LEGACY_GUIDS = {
    { 0xC5E0CDCA, 0x7B6E, 0x41B2, { 0x9F, 0xC4, 0xD9, 0x39, 0x75, 0xCC, 0x46, 0x7B } },
    { 0xF31574D6, 0xB682, 0x4CDC, { 0xBD, 0x56, 0x18, 0x27, 0x86, 0x0A, 0xBE, 0xC6 } },
    { 0xFF72FFDD, 0xBE7E, 0x43FC, { 0x9C, 0x03, 0xAD, 0x81, 0x68, 0x1E, 0x88, 0xE4 } }
};

// Undocumented IVirtualDesktop interface
struct __declspec(uuid("FF72FFDD-BE7E-43FC-9C03-AD81681E88E4")) IVirtualDesktop : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsViewVisible(IUnknown* pView, int* pfVisible) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetID(GUID* pGuid) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetName(void** name) = 0;  // Simplified for now
    virtual HRESULT STDMETHODCALLTYPE GetWallpaperPath(void** path) = 0;  // Simplified for now
};

// Undocumented IVirtualDesktopManagerInternal interface
struct __declspec(uuid("F31574D6-B682-4CDC-BD56-1827860ABEC6")) IVirtualDesktopManagerInternal : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(UINT* pCount) = 0;
    virtual HRESULT STDMETHODCALLTYPE MoveViewToDesktop(IUnknown* pView, IVirtualDesktop* desktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE CanViewMoveDesktops(IUnknown* pView, int* pfCanViewMoveDesktops) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetCurrentDesktop(IVirtualDesktop** desktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetDesktops(IObjectArray** ppDesktops) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetAdjacentDesktop(IVirtualDesktop* pDesktopReference, int uDirection, IVirtualDesktop** ppAdjacentDesktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE SwitchDesktop(IVirtualDesktop* desktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE CreateDesktopW(IVirtualDesktop** ppNewDesktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE RemoveDesktop(IVirtualDesktop* pRemove, IVirtualDesktop* pFallbackDesktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE FindDesktop(GUID* desktopId, IVirtualDesktop** ppDesktop) = 0;
};

// Desktop information structure
struct DesktopInfo {
    GUID id;
    std::wstring name;
    std::wstring wallpaperPath;
    bool isCurrent;
    int index;
    std::vector<HWND> windows;  // Windows on this desktop
    
    DesktopInfo() : isCurrent(false), index(-1) {
        memset(&id, 0, sizeof(GUID));
    }
};

// Process information for desktop isolation
struct ProcessInfo {
    DWORD processId;
    HWND mainWindow;
    std::wstring processName;
    std::wstring commandLine;
    GUID desktopId;
    bool isIsolated;
    
    ProcessInfo() : processId(0), mainWindow(nullptr), isIsolated(false) {
        memset(&desktopId, 0, sizeof(GUID));
    }
};

// Enhanced Virtual Desktop API wrapper class
class VirtualDesktopAPI {
public:
    VirtualDesktopAPI();
    ~VirtualDesktopAPI();

    // Initialization and cleanup
    bool Initialize();
    void Cleanup();
    bool IsInitialized() const { return m_initialized; }

    // Desktop enumeration and information
    std::vector<DesktopInfo> GetAllDesktops();
    DesktopInfo GetCurrentDesktop();
    int GetDesktopCount();
    DesktopInfo GetDesktopByIndex(int index);
    DesktopInfo GetDesktopById(const GUID& id);

    // Desktop management
    bool CreateDesktop(DesktopInfo& newDesktop);
    bool RemoveDesktop(const GUID& desktopId);
    bool SwitchToDesktop(const GUID& desktopId);
    bool SwitchToDesktopByIndex(int index);

    // Window management
    bool MoveWindowToDesktop(HWND hwnd, const GUID& desktopId);
    bool IsWindowOnCurrentDesktop(HWND hwnd);
    GUID GetWindowDesktopId(HWND hwnd);
    std::vector<HWND> GetWindowsOnDesktop(const GUID& desktopId);

    // Process management for isolation
    bool LaunchProcessOnDesktop(const std::wstring& commandLine, const GUID& desktopId, ProcessInfo& processInfo);
    bool TerminateProcessOnDesktop(DWORD processId);
    std::vector<ProcessInfo> GetProcessesOnDesktop(const GUID& desktopId);

    // Utility functions
    std::wstring GuidToString(const GUID& guid);
    GUID StringToGuid(const std::wstring& guidStr);

    // Event callbacks
    using DesktopSwitchCallback = std::function<void(const GUID& fromDesktop, const GUID& toDesktop)>;
    using DesktopCreatedCallback = std::function<void(const GUID& desktopId)>;
    using DesktopRemovedCallback = std::function<void(const GUID& desktopId)>;

    void SetDesktopSwitchCallback(DesktopSwitchCallback callback) { m_switchCallback = callback; }
    void SetDesktopCreatedCallback(DesktopCreatedCallback callback) { m_createdCallback = callback; }
    void SetDesktopRemovedCallback(DesktopRemovedCallback callback) { m_removedCallback = callback; }

private:
    bool m_initialized;
    IVirtualDesktopManager* m_pDesktopManager;
    IVirtualDesktopManagerInternal* m_pDesktopManagerInternal;
    VirtualDesktopGUIDs m_currentGuids;  // Store the working GUID set

    // Event callbacks
    DesktopSwitchCallback m_switchCallback;
    DesktopCreatedCallback m_createdCallback;
    DesktopRemovedCallback m_removedCallback;

    // Helper methods
    bool InitializeCOM();
    void CleanupCOM();
    IVirtualDesktop* GetDesktopInterface(const GUID& desktopId);
    std::wstring GetDesktopName(IVirtualDesktop* pDesktop);
    std::wstring GetDesktopWallpaper(IVirtualDesktop* pDesktop);
    static const VirtualDesktopGUIDs* GetGuidSets();
    
    // Process management helpers
    bool CreateProcessOnDesktop(const std::wstring& commandLine, const GUID& desktopId, PROCESS_INFORMATION& pi);
    std::vector<HWND> EnumerateWindowsOnDesktop(const GUID& desktopId);
};

} // namespace EnhancedVDM

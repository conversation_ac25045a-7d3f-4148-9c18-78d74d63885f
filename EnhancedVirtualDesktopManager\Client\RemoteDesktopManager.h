#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <mutex>
#include "VirtualDesktopAPI.h"
#include "VirtualDesktopManager.h"
#include "ProcessManager.h"
#include "NetworkProtocol.h"

namespace EnhancedVDM {

// Forward declarations
class ClientApplication;

// Remote operation result
struct RemoteOperationResult {
    bool success;
    std::string errorMessage;
    std::vector<uint8_t> data;
    
    RemoteOperationResult(bool s = false, const std::string& msg = "") 
        : success(s), errorMessage(msg) {}
};

// Desktop isolation context
struct IsolationContext {
    GUID desktopId;
    std::string desktopName;
    std::vector<DWORD> isolatedProcesses;
    std::vector<HWND> isolatedWindows;
    bool isActive;
    std::chrono::steady_clock::time_point creationTime;
    
    IsolationContext() : isActive(false) {
        memset(&desktopId, 0, sizeof(GUID));
        creationTime = std::chrono::steady_clock::now();
    }
};

// Remote desktop manager class
class RemoteDesktopManager {
public:
    RemoteDesktopManager();
    ~RemoteDesktopManager();

    // Initialization
    bool Initialize(ClientApplication* clientApp);
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }

    // Desktop operations (remote commands)
    RemoteOperationResult HandleDesktopListRequest();
    RemoteOperationResult HandleDesktopCreateRequest(const DesktopCreateRequest& request);
    RemoteOperationResult HandleDesktopRemoveRequest(const DesktopRemoveRequest& request);
    RemoteOperationResult HandleDesktopSwitchRequest(const DesktopSwitchRequest& request);

    // Process operations (remote commands)
    RemoteOperationResult HandleProcessLaunchRequest(const ProcessLaunchRequest& request);
    RemoteOperationResult HandleProcessTerminateRequest(const ProcessTerminateRequest& request);
    RemoteOperationResult HandleProcessListRequest();

    // Window operations (remote commands)
    RemoteOperationResult HandleWindowMoveRequest(const WindowMoveRequest& request);
    RemoteOperationResult HandleWindowListRequest();

    // Status operations
    RemoteOperationResult HandleStatusRequest();

    // Program isolation features
    RemoteOperationResult LaunchIsolatedProgram(const std::string& programPath, 
                                               const std::string& arguments = "",
                                               const std::string& desktopName = "");
    RemoteOperationResult TerminateIsolatedProgram(DWORD processId);
    RemoteOperationResult SwitchToIsolatedDesktop(const std::string& desktopName);
    RemoteOperationResult ReturnToMainDesktop();

    // Background execution management
    RemoteOperationResult RunProgramInBackground(const std::string& programPath,
                                                const std::string& arguments = "",
                                                bool hideWindow = true);
    RemoteOperationResult BringProgramToForeground(DWORD processId);
    RemoteOperationResult SendProgramToBackground(DWORD processId);

    // Fast switching capabilities
    RemoteOperationResult QuickSwitchToNext();
    RemoteOperationResult QuickSwitchToPrevious();
    RemoteOperationResult QuickSwitchToDesktop(int index);
    RemoteOperationResult ShowDesktopSwitcher();

    // Isolation context management
    std::vector<IsolationContext> GetIsolationContexts() const;
    IsolationContext* GetIsolationContext(const std::string& desktopName);
    bool CreateIsolationContext(const std::string& desktopName, IsolationContext& context);
    bool RemoveIsolationContext(const std::string& desktopName);

    // Desktop state management
    GUID GetCurrentDesktopId() const;
    GUID GetMainDesktopId() const { return m_mainDesktopId; }
    std::vector<DesktopInfo> GetAllDesktops() const;
    bool IsOnMainDesktop() const;

    // Process tracking
    std::vector<ProcessInfo> GetManagedProcesses() const;
    std::vector<ProcessInfo> GetProcessesOnDesktop(const GUID& desktopId) const;
    bool IsProcessManaged(DWORD processId) const;

    // Event callbacks
    using DesktopSwitchedCallback = std::function<void(const GUID& fromDesktop, const GUID& toDesktop)>;
    using ProcessLaunchedCallback = std::function<void(const ProcessInfo& processInfo)>;
    using ProcessTerminatedCallback = std::function<void(DWORD processId, DWORD exitCode)>;

    void SetDesktopSwitchedCallback(DesktopSwitchedCallback callback) { m_desktopSwitchedCallback = callback; }
    void SetProcessLaunchedCallback(ProcessLaunchedCallback callback) { m_processLaunchedCallback = callback; }
    void SetProcessTerminatedCallback(ProcessTerminatedCallback callback) { m_processTerminatedCallback = callback; }

    // Configuration
    void SetAutoHideMainDesktop(bool autoHide) { m_autoHideMainDesktop = autoHide; }
    void SetFastSwitchingEnabled(bool enabled) { m_fastSwitchingEnabled = enabled; }
    void SetProcessIsolationEnabled(bool enabled) { m_processIsolationEnabled = enabled; }

    // Statistics
    size_t GetIsolatedProcessCount() const;
    size_t GetActiveDesktopCount() const;
    std::chrono::steady_clock::duration GetTotalIsolationTime() const;

private:
    bool m_initialized;
    ClientApplication* m_clientApp;
    std::unique_ptr<VirtualDesktopManager> m_desktopManager;
    std::unique_ptr<ProcessManager> m_processManager;
    
    // Desktop state
    GUID m_mainDesktopId;
    GUID m_currentDesktopId;
    std::unordered_map<std::string, IsolationContext> m_isolationContexts;
    mutable std::mutex m_contextsMutex;
    
    // Configuration
    bool m_autoHideMainDesktop;
    bool m_fastSwitchingEnabled;
    bool m_processIsolationEnabled;
    
    // Event callbacks
    DesktopSwitchedCallback m_desktopSwitchedCallback;
    ProcessLaunchedCallback m_processLaunchedCallback;
    ProcessTerminatedCallback m_processTerminatedCallback;
    
    // Statistics
    std::chrono::steady_clock::time_point m_startTime;
    size_t m_totalProcessesLaunched;
    size_t m_totalDesktopSwitches;
    
    // Helper methods
    bool InitializeDesktopManager();
    bool InitializeProcessManager();
    void SetupEventHandlers();
    
    // Desktop management helpers
    bool EnsureMainDesktop();
    bool CreateIsolatedDesktop(const std::string& name, GUID& desktopId);
    bool RemoveIsolatedDesktop(const GUID& desktopId);
    std::string GenerateDesktopName(const std::string& baseName = "Isolated") const;
    
    // Process management helpers
    bool LaunchProcessOnDesktop(const std::string& commandLine, const GUID& desktopId, 
                               ProcessInfo& processInfo, bool hideWindow = false);
    bool MoveProcessToDesktop(DWORD processId, const GUID& desktopId);
    bool TerminateProcess(DWORD processId, bool force = false);
    
    // Window management helpers
    std::vector<HWND> GetWindowsOnDesktop(const GUID& desktopId) const;
    bool MoveWindowToDesktop(HWND hwnd, const GUID& desktopId);
    bool HideWindow(HWND hwnd);
    bool ShowWindow(HWND hwnd);
    
    // Event handlers
    void OnDesktopSwitched(const GUID& fromDesktop, const GUID& toDesktop);
    void OnProcessLaunched(const ProcessInfo& processInfo);
    void OnProcessTerminated(DWORD processId, DWORD exitCode);
    
    // Serialization helpers
    std::vector<uint8_t> SerializeDesktopList(const std::vector<DesktopInfo>& desktops) const;
    std::vector<uint8_t> SerializeProcessList(const std::vector<ProcessInfo>& processes) const;
    std::vector<uint8_t> SerializeWindowList(const std::vector<HWND>& windows) const;
    std::vector<uint8_t> SerializeOperationResponse(const OperationResponse& response) const;
    
    // Error handling
    RemoteOperationResult CreateErrorResult(const std::string& error, DWORD errorCode = 0) const;
    RemoteOperationResult CreateSuccessResult(const std::string& message = "", 
                                             const std::vector<uint8_t>& data = {}) const;
    
    // Validation
    bool ValidateDesktopId(const GUID& desktopId) const;
    bool ValidateProcessId(DWORD processId) const;
    bool ValidateWindowHandle(HWND hwnd) const;
    bool ValidateCommandLine(const std::string& commandLine) const;
};

} // namespace EnhancedVDM

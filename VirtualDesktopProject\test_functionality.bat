@echo off
echo ========================================
echo Virtual Desktop Manager - Functionality Test
echo ========================================
echo.

set EXE=.\bin\Release\x64\VirtualDesktopManager.exe

echo Testing basic functionality...
echo.

echo 1. Showing version information:
echo --------------------------------
%EXE% version
echo.

echo 2. Showing help:
echo ----------------
%EXE% help
echo.

echo 3. Listing current virtual desktops:
echo ------------------------------------
%EXE% list
echo.

echo 4. Getting current desktop:
echo ---------------------------
%EXE% current
echo.

echo 5. Getting desktop count:
echo -------------------------
%EXE% count
echo.

echo 6. Testing invalid command:
echo ---------------------------
%EXE% invalid_command
echo.

echo ========================================
echo Test completed!
echo ========================================
echo.
echo Note: Advanced features like creating, switching, and moving
echo windows between desktops require the undocumented Windows
echo Virtual Desktop APIs to be available on your system.
echo.
echo Current system shows limited functionality due to API
echo availability, but the application framework is complete
echo and ready for systems that support the full API set.
echo.
pause

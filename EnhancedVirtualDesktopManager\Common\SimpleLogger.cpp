#include "SimpleLogger.h"
#include <stdarg.h>
#include <stdio.h>

namespace EnhancedVDM {

bool SimpleLogger::initialized_ = false;
HANDLE SimpleLogger::log_file_ = INVALID_HANDLE_VALUE;

void SimpleLogger::Initialize(const char* log_path) noexcept {
    if (initialized_) return;
    
    try {
        log_file_ = CreateFileA(
            log_path,
            GENERIC_WRITE,
            FILE_SHARE_READ,
            nullptr,
            CREATE_ALWAYS,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );
        
        if (log_file_ != INVALID_HANDLE_VALUE) {
            initialized_ = true;
            Log(LogLevel::Info, "Enhanced Virtual Desktop Manager logging initialized");
        }
    } catch (...) {
        // Silent failure for logging initialization
    }
}

void SimpleLogger::Log(LogLevel level, const char* format, ...) noexcept {
    if (!initialized_ || log_file_ == INVALID_HANDLE_VALUE) return;
    
    try {
        va_list args;
        va_start(args, format);
        LogInternal(level, format, args);
        va_end(args);
    } catch (...) {
        // Silent failure for logging
    }
}

void SimpleLogger::LogNetwork(const char* format, ...) noexcept {
    if (!initialized_ || log_file_ == INVALID_HANDLE_VALUE) return;
    
    try {
        va_list args;
        va_start(args, format);
        LogInternal(LogLevel::Network, format, args);
        va_end(args);
    } catch (...) {
        // Silent failure for logging
    }
}

void SimpleLogger::LogSecurity(const char* format, ...) noexcept {
    if (!initialized_ || log_file_ == INVALID_HANDLE_VALUE) return;
    
    try {
        va_list args;
        va_start(args, format);
        LogInternal(LogLevel::Security, format, args);
        va_end(args);
    } catch (...) {
        // Silent failure for logging
    }
}

void SimpleLogger::LogInternal(LogLevel level, const char* format, va_list args) noexcept {
    try {
        char buffer[MAX_LOG_SIZE];
        
        const auto timestamp = GetTimestamp();
        const auto level_str = GetLogLevelString(level);
        
        int written = _snprintf_s(buffer, sizeof(buffer), _TRUNCATE,
            "[%s] [%s] ", timestamp.c_str(), level_str);
        
        if (written > 0 && written < static_cast<int>(sizeof(buffer))) {
            written += _vsnprintf_s(buffer + written, sizeof(buffer) - written, 
                _TRUNCATE, format, args);
        }
        
        if (written > 0) {
            // Add newline
            if (written < static_cast<int>(sizeof(buffer)) - 2) {
                buffer[written++] = '\r';
                buffer[written++] = '\n';
            }
            
            DWORD bytes_written;
            WriteFile(log_file_, buffer, written, &bytes_written, nullptr);
            FlushFileBuffers(log_file_);
        }
    } catch (...) {
        // Silent failure for logging
    }
}

void SimpleLogger::LogAppStart(const char* app_name, const char* path) noexcept {
    if (path) {
        Log(LogLevel::Info, "Starting application: %s (Path: %s)", app_name, path);
    } else {
        Log(LogLevel::Info, "Starting application: %s", app_name);
    }
}

void SimpleLogger::LogAppEnd(const char* app_name, DWORD exit_code) noexcept {
    Log(LogLevel::Info, "Application ended: %s (Exit code: %lu)", app_name, exit_code);
}

void SimpleLogger::Shutdown() noexcept {
    if (initialized_) {
        Log(LogLevel::Info, "Enhanced Virtual Desktop Manager logging shutdown");
        if (log_file_ != INVALID_HANDLE_VALUE) {
            CloseHandle(log_file_);
            log_file_ = INVALID_HANDLE_VALUE;
        }
        initialized_ = false;
    }
}

const char* SimpleLogger::GetLogLevelString(LogLevel level) noexcept {
    switch (level) {
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Network: return "NETWORK";
        case LogLevel::Security: return "SECURITY";
        default: return "UNKNOWN";
    }
}

std::string SimpleLogger::GetTimestamp() noexcept {
    try {
        SYSTEMTIME st;
        GetLocalTime(&st);
        
        char timestamp[64];
        _snprintf_s(timestamp, sizeof(timestamp), _TRUNCATE,
            "%04d-%02d-%02d %02d:%02d:%02d.%03d",
            st.wYear, st.wMonth, st.wDay,
            st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);
        
        return std::string(timestamp);
    } catch (...) {
        return "0000-00-00 00:00:00.000";
    }
}

} // namespace EnhancedVDM

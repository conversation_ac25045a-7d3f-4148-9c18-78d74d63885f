#pragma once

#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include "VirtualDesktopAPI.h"

#pragma comment(lib, "ws2_32.lib")

namespace EnhancedVDM {

// Network protocol constants
constexpr int DEFAULT_PORT = 8888;
constexpr int MAX_PACKET_SIZE = 8192;
constexpr int MAX_CLIENTS = 50;
constexpr int HEARTBEAT_INTERVAL = 30000; // 30 seconds

// Message types for client-server communication
enum class MessageType : uint32_t {
    // Authentication
    AUTH_REQUEST = 0x1000,
    AUTH_RESPONSE = 0x1001,
    AUTH_SUCCESS = 0x1002,
    AUTH_FAILURE = 0x1003,

    // Desktop operations
    DESKTOP_LIST_REQUEST = 0x2000,
    DESKTOP_LIST_RESPONSE = 0x2001,
    DESKTOP_CREATE_REQUEST = 0x2002,
    DESKTOP_CREATE_RESPONSE = 0x2003,
    DESKTOP_REMOVE_REQUEST = 0x2004,
    DESKTOP_REMOVE_RESPONSE = 0x2005,
    DESKTOP_SWITCH_REQUEST = 0x2006,
    DESKTOP_SWITCH_RESPONSE = 0x2007,

    // Process operations
    PROCESS_LAUNCH_REQUEST = 0x3000,
    PROCESS_LAUNCH_RESPONSE = 0x3001,
    PROCESS_TERMINATE_REQUEST = 0x3002,
    PROCESS_TERMINATE_RESPONSE = 0x3003,
    PROCESS_LIST_REQUEST = 0x3004,
    PROCESS_LIST_RESPONSE = 0x3005,

    // Window operations
    WINDOW_MOVE_REQUEST = 0x4000,
    WINDOW_MOVE_RESPONSE = 0x4001,
    WINDOW_LIST_REQUEST = 0x4002,
    WINDOW_LIST_RESPONSE = 0x4003,

    // Status and monitoring
    STATUS_REQUEST = 0x5000,
    STATUS_RESPONSE = 0x5001,
    HEARTBEAT = 0x5002,
    NOTIFICATION = 0x5003,

    // Error handling
    ERROR_RESPONSE = 0x9000,
    UNKNOWN_COMMAND = 0x9001
};

// Message header structure
#pragma pack(push, 1)
struct MessageHeader {
    uint32_t magic;           // Magic number for validation
    uint32_t messageType;     // MessageType enum value
    uint32_t dataSize;        // Size of data following header
    uint32_t sequenceId;      // Sequence ID for request/response matching
    uint32_t checksum;        // Simple checksum for data integrity
    
    static constexpr uint32_t MAGIC_NUMBER = 0xDEADBEEF;
    
    MessageHeader() : magic(MAGIC_NUMBER), messageType(0), dataSize(0), sequenceId(0), checksum(0) {}
    MessageHeader(MessageType type, uint32_t size, uint32_t seq = 0) 
        : magic(MAGIC_NUMBER), messageType(static_cast<uint32_t>(type)), dataSize(size), sequenceId(seq), checksum(0) {}
};
#pragma pack(pop)

// Authentication data
struct AuthRequest {
    char username[64];
    char passwordHash[64];  // SHA-256 hash
    char clientVersion[32];
    char clientName[128];
};

struct AuthResponse {
    bool success;
    char sessionToken[64];
    char serverVersion[32];
    uint32_t permissions;
};

// Desktop operation data structures
struct DesktopCreateRequest {
    char desktopName[128];
};

struct DesktopRemoveRequest {
    GUID desktopId;
};

struct DesktopSwitchRequest {
    GUID desktopId;
};

// Process operation data structures
struct ProcessLaunchRequest {
    char commandLine[512];
    char workingDirectory[260];
    GUID desktopId;
    bool hideWindow;
    bool isolateProcess;
};

struct ProcessTerminateRequest {
    DWORD processId;
    bool forceTerminate;
};

// Window operation data structures
struct WindowMoveRequest {
    HWND windowHandle;
    GUID desktopId;
};

// Response structures
struct OperationResponse {
    bool success;
    uint32_t errorCode;
    char errorMessage[256];
};

// Client information
struct ClientInfo {
    SOCKET socket;
    std::string address;
    uint16_t port;
    std::string username;
    std::string sessionToken;
    std::string clientName;
    std::string clientVersion;
    uint32_t permissions;
    std::chrono::steady_clock::time_point lastHeartbeat;
    bool authenticated;
    std::atomic<bool> connected;
    
    ClientInfo() : socket(INVALID_SOCKET), port(0), permissions(0), authenticated(false), connected(false) {}
};

// Network server class
class NetworkServer {
public:
    NetworkServer();
    ~NetworkServer();

    // Server lifecycle
    bool Initialize(int port = DEFAULT_PORT);
    bool Start();
    void Stop();
    void Shutdown();

    // Client management
    std::vector<std::shared_ptr<ClientInfo>> GetConnectedClients();
    bool DisconnectClient(const std::string& sessionToken);
    void BroadcastMessage(MessageType type, const void* data, size_t dataSize);

    // Message handling callbacks
    using MessageHandler = std::function<void(std::shared_ptr<ClientInfo>, MessageType, const void*, size_t)>;
    void SetMessageHandler(MessageHandler handler) { m_messageHandler = handler; }

    using ClientConnectedHandler = std::function<void(std::shared_ptr<ClientInfo>)>;
    void SetClientConnectedHandler(ClientConnectedHandler handler) { m_clientConnectedHandler = handler; }

    using ClientDisconnectedHandler = std::function<void(std::shared_ptr<ClientInfo>)>;
    void SetClientDisconnectedHandler(ClientDisconnectedHandler handler) { m_clientDisconnectedHandler = handler; }

private:
    SOCKET m_serverSocket;
    int m_port;
    std::atomic<bool> m_running;
    std::vector<std::shared_ptr<ClientInfo>> m_clients;
    std::mutex m_clientsMutex;
    std::thread m_acceptThread;
    std::thread m_heartbeatThread;

    MessageHandler m_messageHandler;
    ClientConnectedHandler m_clientConnectedHandler;
    ClientDisconnectedHandler m_clientDisconnectedHandler;

    // Internal methods
    void AcceptClients();
    void HandleClient(std::shared_ptr<ClientInfo> client);
    void HeartbeatMonitor();
    bool SendMessage(SOCKET socket, MessageType type, const void* data, size_t dataSize, uint32_t sequenceId = 0);
    bool ReceiveMessage(SOCKET socket, MessageHeader& header, std::vector<uint8_t>& data);
    uint32_t CalculateChecksum(const void* data, size_t size);
    void RemoveClient(std::shared_ptr<ClientInfo> client);
};

// Network client class
class NetworkClient {
public:
    NetworkClient();
    ~NetworkClient();

    // Client lifecycle
    bool Connect(const std::string& serverAddress, int port = DEFAULT_PORT);
    void Disconnect();
    bool IsConnected() const { return m_connected; }

    // Authentication
    bool Authenticate(const std::string& username, const std::string& password, const std::string& clientName);

    // Message sending
    bool SendMessage(MessageType type, const void* data, size_t dataSize);
    bool SendRequest(MessageType type, const void* data, size_t dataSize, MessageType expectedResponse, 
                    std::vector<uint8_t>& responseData, uint32_t timeoutMs = 5000);

    // Message handling callbacks
    using MessageHandler = std::function<void(MessageType, const void*, size_t)>;
    void SetMessageHandler(MessageHandler handler) { m_messageHandler = handler; }

    using DisconnectedHandler = std::function<void()>;
    void SetDisconnectedHandler(DisconnectedHandler handler) { m_disconnectedHandler = handler; }

private:
    SOCKET m_socket;
    std::string m_serverAddress;
    int m_port;
    std::atomic<bool> m_connected;
    std::string m_sessionToken;
    std::thread m_receiveThread;
    std::mutex m_sendMutex;
    uint32_t m_sequenceCounter;

    MessageHandler m_messageHandler;
    DisconnectedHandler m_disconnectedHandler;

    // Internal methods
    void ReceiveMessages();
    bool SendMessageInternal(MessageType type, const void* data, size_t dataSize, uint32_t sequenceId = 0);
    bool ReceiveMessage(MessageHeader& header, std::vector<uint8_t>& data);
    uint32_t CalculateChecksum(const void* data, size_t size);
    uint32_t GetNextSequenceId() { return ++m_sequenceCounter; }
};

// Utility functions
namespace NetworkUtils {
    bool InitializeWinsock();
    void CleanupWinsock();
    std::string GetLocalIPAddress();
    std::string GetErrorString(int errorCode);
    bool IsValidIPAddress(const std::string& address);
    std::vector<uint8_t> SerializeDesktopInfo(const DesktopInfo& desktop);
    DesktopInfo DeserializeDesktopInfo(const std::vector<uint8_t>& data);
    std::vector<uint8_t> SerializeProcessInfo(const ProcessInfo& process);
    ProcessInfo DeserializeProcessInfo(const std::vector<uint8_t>& data);
}

} // namespace EnhancedVDM

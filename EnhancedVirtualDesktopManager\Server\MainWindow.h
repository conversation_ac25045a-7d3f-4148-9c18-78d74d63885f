#pragma once

#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>
#include <memory>
#include "NetworkProtocol.h"

namespace EnhancedVDM {

// Forward declarations
class ServerApplication;

// Control IDs
enum ControlID {
    // Menu items
    ID_FILE_START_SERVER = 1001,
    ID_FILE_STOP_SERVER = 1002,
    ID_FILE_EXIT = 1003,
    ID_EDIT_SETTINGS = 1004,
    ID_VIEW_CLIENTS = 1005,
    ID_VIEW_LOGS = 1006,
    ID_VIEW_STATISTICS = 1007,
    ID_HELP_ABOUT = 1008,
    
    // Toolbar buttons
    ID_TOOLBAR_START = 2001,
    ID_TOOLBAR_STOP = 2002,
    ID_TOOLBAR_SETTINGS = 2003,
    ID_TOOLBAR_REFRESH = 2004,
    
    // Controls
    ID_LISTVIEW_CLIENTS = 3001,
    ID_LISTVIEW_LOGS = 3002,
    ID_EDIT_COMMAND = 3003,
    ID_BUTTON_SEND = 3004,
    ID_STATUSBAR = 3005,
    ID_TAB_CONTROL = 3006,
    
    // Context menu
    ID_CONTEXT_DISCONNECT = 4001,
    ID_CONTEXT_SEND_MESSAGE = 4002,
    ID_CONTEXT_VIEW_DETAILS = 4003,
    ID_CONTEXT_COPY_INFO = 4004
};

// Tab pages
enum TabPage {
    TAB_CLIENTS = 0,
    TAB_LOGS = 1,
    TAB_STATISTICS = 2,
    TAB_SETTINGS = 3
};

// List view columns for clients
enum ClientColumn {
    COL_CLIENT_NAME = 0,
    COL_CLIENT_ADDRESS = 1,
    COL_CLIENT_USER = 2,
    COL_CLIENT_STATUS = 3,
    COL_CLIENT_CONNECTED = 4,
    COL_CLIENT_LAST_ACTIVITY = 5
};

// List view columns for logs
enum LogColumn {
    COL_LOG_TIME = 0,
    COL_LOG_LEVEL = 1,
    COL_LOG_SOURCE = 2,
    COL_LOG_MESSAGE = 3
};

// Main window class
class MainWindow {
public:
    MainWindow();
    ~MainWindow();

    // Window lifecycle
    bool Create(HINSTANCE hInstance, ServerApplication* app);
    void Show(int nCmdShow = SW_SHOW);
    void Hide();
    void Destroy();
    HWND GetHandle() const { return m_hwnd; }

    // UI updates
    void UpdateClientList();
    void UpdateLogList();
    void UpdateStatistics();
    void UpdateServerStatus(bool running);
    void UpdateStatusBar(const std::string& message);

    // Client management
    void AddClient(std::shared_ptr<ClientInfo> client);
    void RemoveClient(const std::string& sessionToken);
    void UpdateClient(std::shared_ptr<ClientInfo> client);

    // Logging
    void AddLogEntry(const std::string& timestamp, const std::string& level,
                    const std::string& source, const std::string& message);
    void ClearLogs();

    // Notifications
    void ShowNotification(const std::string& title, const std::string& message);
    void ShowError(const std::string& error);
    void ShowInfo(const std::string& info);

    // Dialog management
    void ShowSettingsDialog();
    void ShowAboutDialog();
    void ShowClientDetailsDialog(const std::string& sessionToken);

private:
    HWND m_hwnd;
    HINSTANCE m_hInstance;
    ServerApplication* m_app;
    
    // Child controls
    HWND m_hToolbar;
    HWND m_hTabControl;
    HWND m_hStatusBar;
    HWND m_hClientList;
    HWND m_hLogList;
    HWND m_hCommandEdit;
    HWND m_hSendButton;
    
    // Tab page windows
    HWND m_hTabPages[4];
    int m_currentTab;
    
    // UI state
    bool m_serverRunning;
    std::vector<std::shared_ptr<ClientInfo>> m_clients;
    
    // Window procedure
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    LRESULT HandleMessage(UINT msg, WPARAM wParam, LPARAM lParam);
    
    // Message handlers
    LRESULT OnCreate(WPARAM wParam, LPARAM lParam);
    LRESULT OnDestroy(WPARAM wParam, LPARAM lParam);
    LRESULT OnSize(WPARAM wParam, LPARAM lParam);
    LRESULT OnCommand(WPARAM wParam, LPARAM lParam);
    LRESULT OnNotify(WPARAM wParam, LPARAM lParam);
    LRESULT OnContextMenu(WPARAM wParam, LPARAM lParam);
    LRESULT OnClose(WPARAM wParam, LPARAM lParam);
    LRESULT OnSystemTray(WPARAM wParam, LPARAM lParam);
    
    // Control creation
    bool CreateControls();
    bool CreateMenuBar();
    bool CreateToolbar();
    bool CreateTabControl();
    bool CreateStatusBar();
    bool CreateClientListView();
    bool CreateLogListView();
    bool CreateTabPages();
    
    // Layout management
    void LayoutControls();
    void ResizeTabPages();
    RECT GetTabPageRect();
    
    // Tab management
    void OnTabChanged();
    void ShowTabPage(int tabIndex);
    void HideAllTabPages();
    
    // List view management
    void InitializeClientListView();
    void InitializeLogListView();
    void AddClientToList(std::shared_ptr<ClientInfo> client);
    void UpdateClientInList(std::shared_ptr<ClientInfo> client);
    void RemoveClientFromList(const std::string& sessionToken);
    int FindClientInList(const std::string& sessionToken);
    
    // Command handling
    void OnStartServer();
    void OnStopServer();
    void OnSettings();
    void OnRefresh();
    void OnSendCommand();
    void OnDisconnectClient();
    void OnViewClientDetails();
    void OnCopyClientInfo();
    void OnExit();
    void OnAbout();
    
    // Context menu
    void ShowClientContextMenu(POINT pt);
    HMENU CreateClientContextMenu();
    
    // Utility functions
    std::string GetSelectedClientToken();
    std::shared_ptr<ClientInfo> GetSelectedClient();
    void SetControlText(int controlId, const std::string& text);
    std::string GetControlText(int controlId);
    void EnableControl(int controlId, bool enabled);
    
    // String formatting
    std::string FormatClientStatus(std::shared_ptr<ClientInfo> client);
    std::string FormatTimeStamp(const std::chrono::steady_clock::time_point& time);
    std::string FormatDuration(const std::chrono::steady_clock::duration& duration);
    std::string FormatBytes(size_t bytes);
    
    // Resource management
    HICON LoadApplicationIcon();
    HIMAGELIST CreateToolbarImageList();
    void LoadAccelerators();
    
    // Constants
    static constexpr int TOOLBAR_HEIGHT = 32;
    static constexpr int STATUSBAR_HEIGHT = 24;
    static constexpr int TAB_HEIGHT = 24;
    static constexpr int MARGIN = 8;
};

} // namespace EnhancedVDM

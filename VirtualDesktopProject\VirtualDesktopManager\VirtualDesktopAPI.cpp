#include "VirtualDesktopAPI.h"
#include "../common/SimpleLogger.h"
#include <comdef.h>
#include <shlobj.h>
#include <propvarutil.h>
#include <sstream>
#include <iomanip>

namespace VirtualDesktop {

VirtualDesktopAPI::VirtualDesktopAPI()
    : m_initialized(false)
    , m_pDesktopManager(nullptr)
    , m_pDesktopManagerInternal(nullptr)
    , m_currentGuids(LEGACY_GUIDS)  // Default to legacy GUIDs
{
}

VirtualDesktopAPI::~VirtualDesktopAPI()
{
    Cleanup();
}

bool VirtualDesktopAPI::Initialize()
{
    if (m_initialized) {
        return true;
    }

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Initializing Virtual Desktop API");

    if (!InitializeCOM()) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to initialize COM");
        return false;
    }

    // Create the documented IVirtualDesktopManager interface
    HRESULT hr = CoCreateInstance(CLSID_VirtualDesktopManager, nullptr, CLSCTX_LOCAL_SERVER,
        IID_IVirtualDesktopManager, reinterpret_cast<void**>(&m_pDesktopManager));

    if (FAILED(hr)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Failed to create IVirtualDesktopManager: 0x%08X (Virtual Desktop features may not be available on this system)", hr);

        // Try alternative approaches or continue without the interface
        // For now, we'll continue without it and provide limited functionality
        m_pDesktopManager = nullptr;
    }

    // Try to create the undocumented IVirtualDesktopManagerInternal interface
    // Try different GUID sets for different Windows versions
    const VirtualDesktopGUIDs* guidSets[] = { &WIN11_22H2_GUIDS, &LEGACY_GUIDS };

    for (const auto* guids : guidSets) {
        hr = CoCreateInstance(guids->CLSID_VirtualDesktopManagerInternal, nullptr, CLSCTX_LOCAL_SERVER,
            guids->IID_IVirtualDesktopManagerInternal, reinterpret_cast<void**>(&m_pDesktopManagerInternal));

        if (SUCCEEDED(hr)) {
            m_currentGuids = *guids;  // Store the working GUID set
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
                "Successfully created IVirtualDesktopManagerInternal with GUID set");
            break;
        }
    }

    if (FAILED(hr)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Failed to create IVirtualDesktopManagerInternal with all GUID sets: 0x%08X (some features may be limited)", hr);
        // Continue without the internal interface - we can still use basic functionality
    }

    // Even if internal interface failed, we can still work with the documented interface
    m_initialized = true;
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Virtual Desktop API initialized successfully");
    return true;
}

void VirtualDesktopAPI::Cleanup()
{
    if (!m_initialized) {
        return;
    }

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Cleaning up Virtual Desktop API");

    if (m_pDesktopManagerInternal) {
        m_pDesktopManagerInternal->Release();
        m_pDesktopManagerInternal = nullptr;
    }

    if (m_pDesktopManager) {
        m_pDesktopManager->Release();
        m_pDesktopManager = nullptr;
    }

    CleanupCOM();
    m_initialized = false;
}

bool VirtualDesktopAPI::InitializeCOM()
{
    HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
    if (FAILED(hr) && hr != RPC_E_CHANGED_MODE) {
        return false;
    }
    return true;
}

void VirtualDesktopAPI::CleanupCOM()
{
    CoUninitialize();
}

std::vector<DesktopInfo> VirtualDesktopAPI::GetAllDesktops()
{
    std::vector<DesktopInfo> desktops;

    if (!m_initialized) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Virtual Desktop API not initialized");
        return desktops;
    }

    if (!m_pDesktopManagerInternal) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Internal interface not available - returning minimal desktop info");

        // Create a single desktop entry representing the current desktop
        DesktopInfo info = {};
        info.index = 0;
        info.isCurrent = true;
        info.name = L"Current Desktop";
        info.wallpaperPath = L"";
        // Generate a dummy GUID for the current desktop
        CoCreateGuid(&info.id);
        desktops.push_back(info);
        return desktops;
    }

    IObjectArray* pDesktopArray = nullptr;
    HRESULT hr = m_pDesktopManagerInternal->GetDesktops(&pDesktopArray);
    if (FAILED(hr)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "Failed to get desktop array: 0x%08X", hr);
        return desktops;
    }

    UINT count = 0;
    hr = pDesktopArray->GetCount(&count);
    if (FAILED(hr)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "Failed to get desktop count: 0x%08X", hr);
        pDesktopArray->Release();
        return desktops;
    }

    // Get current desktop for comparison
    IVirtualDesktop* pCurrentDesktop = nullptr;
    m_pDesktopManagerInternal->GetCurrentDesktop(&pCurrentDesktop);
    GUID currentDesktopId = {};
    if (pCurrentDesktop) {
        pCurrentDesktop->GetID(&currentDesktopId);
    }

    for (UINT i = 0; i < count; ++i) {
        IVirtualDesktop* pDesktop = nullptr;
        hr = pDesktopArray->GetAt(i, m_currentGuids.IID_IVirtualDesktop, reinterpret_cast<void**>(&pDesktop));
        if (SUCCEEDED(hr) && pDesktop) {
            DesktopInfo info = {};
            info.index = static_cast<int>(i);
            
            if (SUCCEEDED(pDesktop->GetID(&info.id))) {
                info.isCurrent = (memcmp(&info.id, &currentDesktopId, sizeof(GUID)) == 0);
                info.name = GetDesktopName(pDesktop);
                info.wallpaperPath = GetDesktopWallpaper(pDesktop);
                desktops.push_back(info);
            }
            
            pDesktop->Release();
        }
    }

    if (pCurrentDesktop) {
        pCurrentDesktop->Release();
    }
    pDesktopArray->Release();

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, 
        "Retrieved %zu virtual desktops", desktops.size());
    return desktops;
}

DesktopInfo VirtualDesktopAPI::GetCurrentDesktop()
{
    DesktopInfo info = {};

    if (!m_initialized) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Virtual Desktop API not initialized");
        return info;
    }

    if (!m_pDesktopManagerInternal) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Internal interface not available - returning minimal current desktop info");

        info.index = 0;
        info.isCurrent = true;
        info.name = L"Current Desktop";
        info.wallpaperPath = L"";
        CoCreateGuid(&info.id);
        return info;
    }

    IVirtualDesktop* pCurrentDesktop = nullptr;
    HRESULT hr = m_pDesktopManagerInternal->GetCurrentDesktop(&pCurrentDesktop);
    if (FAILED(hr) || !pCurrentDesktop) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "Failed to get current desktop: 0x%08X", hr);
        return info;
    }

    info.isCurrent = true;
    if (SUCCEEDED(pCurrentDesktop->GetID(&info.id))) {
        info.name = GetDesktopName(pCurrentDesktop);
        info.wallpaperPath = GetDesktopWallpaper(pCurrentDesktop);
        
        // Find the index by comparing with all desktops
        auto allDesktops = GetAllDesktops();
        for (const auto& desktop : allDesktops) {
            if (memcmp(&desktop.id, &info.id, sizeof(GUID)) == 0) {
                info.index = desktop.index;
                break;
            }
        }
    }

    pCurrentDesktop->Release();
    return info;
}

int VirtualDesktopAPI::GetDesktopCount()
{
    if (!m_initialized) {
        return 0;
    }

    if (!m_pDesktopManagerInternal) {
        // Without internal interface, we can only report 1 desktop
        return 1;
    }

    UINT count = 0;
    HRESULT hr = m_pDesktopManagerInternal->GetCount(&count);
    if (FAILED(hr)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Failed to get desktop count: 0x%08X", hr);
        return 1;  // Fallback to 1 desktop
    }

    return static_cast<int>(count);
}

DesktopInfo VirtualDesktopAPI::GetDesktopByIndex(int index)
{
    DesktopInfo info = {};
    auto allDesktops = GetAllDesktops();
    
    if (index >= 0 && index < static_cast<int>(allDesktops.size())) {
        info = allDesktops[index];
    }
    
    return info;
}

DesktopInfo VirtualDesktopAPI::GetDesktopById(const GUID& id)
{
    DesktopInfo info = {};
    auto allDesktops = GetAllDesktops();
    
    for (const auto& desktop : allDesktops) {
        if (memcmp(&desktop.id, &id, sizeof(GUID)) == 0) {
            info = desktop;
            break;
        }
    }
    
    return info;
}

bool VirtualDesktopAPI::CreateDesktop(DesktopInfo& newDesktop)
{
    if (!m_initialized || !m_pDesktopManagerInternal) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "Virtual Desktop API not initialized or internal interface not available");
        return false;
    }

    IVirtualDesktop* pNewDesktop = nullptr;
    HRESULT hr = m_pDesktopManagerInternal->CreateDesktopW(&pNewDesktop);
    if (FAILED(hr) || !pNewDesktop) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "Failed to create new desktop: 0x%08X", hr);
        return false;
    }

    // Get the new desktop's information
    if (SUCCEEDED(pNewDesktop->GetID(&newDesktop.id))) {
        newDesktop.name = GetDesktopName(pNewDesktop);
        newDesktop.wallpaperPath = GetDesktopWallpaper(pNewDesktop);
        newDesktop.isCurrent = false;
        newDesktop.index = GetDesktopCount() - 1; // New desktop is typically added at the end
        
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, 
            "Created new virtual desktop with ID: %s", GuidToString(newDesktop.id).c_str());
    }

    pNewDesktop->Release();
    return true;
}

bool VirtualDesktopAPI::RemoveDesktop(const GUID& desktopId)
{
    if (!m_initialized || !m_pDesktopManagerInternal) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Virtual Desktop API not initialized or internal interface not available");
        return false;
    }

    // Cannot remove if there's only one desktop
    if (GetDesktopCount() <= 1) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Cannot remove desktop: only one desktop remaining");
        return false;
    }

    IVirtualDesktop* pDesktopToRemove = GetDesktopInterface(desktopId);
    if (!pDesktopToRemove) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Desktop with specified ID not found");
        return false;
    }

    // Get a fallback desktop (current desktop if different, or first available)
    IVirtualDesktop* pFallbackDesktop = nullptr;
    auto currentDesktop = GetCurrentDesktop();

    if (memcmp(&currentDesktop.id, &desktopId, sizeof(GUID)) != 0) {
        // Use current desktop as fallback
        m_pDesktopManagerInternal->GetCurrentDesktop(&pFallbackDesktop);
    } else {
        // Find another desktop to use as fallback
        auto allDesktops = GetAllDesktops();
        for (const auto& desktop : allDesktops) {
            if (memcmp(&desktop.id, &desktopId, sizeof(GUID)) != 0) {
                pFallbackDesktop = GetDesktopInterface(desktop.id);
                break;
            }
        }
    }

    if (!pFallbackDesktop) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Could not find fallback desktop");
        pDesktopToRemove->Release();
        return false;
    }

    HRESULT hr = m_pDesktopManagerInternal->RemoveDesktop(pDesktopToRemove, pFallbackDesktop);
    bool success = SUCCEEDED(hr);

    if (success) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
            "Successfully removed virtual desktop with ID: %s", GuidToString(desktopId).c_str());
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Failed to remove desktop: 0x%08X", hr);
    }

    pDesktopToRemove->Release();
    pFallbackDesktop->Release();
    return success;
}

bool VirtualDesktopAPI::SwitchToDesktop(const GUID& desktopId)
{
    if (!m_initialized || !m_pDesktopManagerInternal) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Virtual Desktop API not initialized or internal interface not available");
        return false;
    }

    IVirtualDesktop* pDesktop = GetDesktopInterface(desktopId);
    if (!pDesktop) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Desktop with specified ID not found");
        return false;
    }

    HRESULT hr = m_pDesktopManagerInternal->SwitchDesktop(pDesktop);
    bool success = SUCCEEDED(hr);

    if (success) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
            "Successfully switched to virtual desktop with ID: %s", GuidToString(desktopId).c_str());
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Failed to switch to desktop: 0x%08X", hr);
    }

    pDesktop->Release();
    return success;
}

bool VirtualDesktopAPI::SwitchToDesktopByIndex(int index)
{
    auto desktop = GetDesktopByIndex(index);
    if (desktop.index == -1) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Invalid desktop index: %d", index);
        return false;
    }

    return SwitchToDesktop(desktop.id);
}

bool VirtualDesktopAPI::MoveWindowToDesktop(HWND hwnd, const GUID& desktopId)
{
    if (!m_initialized) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Virtual Desktop API not initialized");
        return false;
    }

    if (!m_pDesktopManager) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Desktop Manager not available - window move operation not supported");
        return false;
    }

    if (!IsWindow(hwnd)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Invalid window handle");
        return false;
    }

    HRESULT hr = m_pDesktopManager->MoveWindowToDesktop(hwnd, desktopId);
    bool success = SUCCEEDED(hr);

    if (success) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
            "Successfully moved window 0x%p to desktop %s", hwnd, GuidToString(desktopId).c_str());
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Failed to move window to desktop: 0x%08X", hr);
    }

    return success;
}

bool VirtualDesktopAPI::IsWindowOnCurrentDesktop(HWND hwnd)
{
    if (!m_initialized || !m_pDesktopManager) {
        return false;
    }

    if (!IsWindow(hwnd)) {
        return false;
    }

    BOOL isOnCurrent = FALSE;
    HRESULT hr = m_pDesktopManager->IsWindowOnCurrentVirtualDesktop(hwnd, &isOnCurrent);

    return SUCCEEDED(hr) && isOnCurrent;
}

GUID VirtualDesktopAPI::GetWindowDesktopId(HWND hwnd)
{
    GUID desktopId = {};

    if (!m_initialized || !m_pDesktopManager) {
        return desktopId;
    }

    if (!IsWindow(hwnd)) {
        return desktopId;
    }

    HRESULT hr = m_pDesktopManager->GetWindowDesktopId(hwnd, &desktopId);
    if (FAILED(hr)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Failed to get window desktop ID: 0x%08X", hr);
    }

    return desktopId;
}

std::wstring VirtualDesktopAPI::GuidToString(const GUID& guid)
{
    wchar_t guidStr[64];
    StringFromGUID2(guid, guidStr, ARRAYSIZE(guidStr));
    return std::wstring(guidStr);
}

GUID VirtualDesktopAPI::StringToGuid(const std::wstring& guidStr)
{
    GUID guid = {};
    HRESULT hr = CLSIDFromString(guidStr.c_str(), &guid);
    if (FAILED(hr)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Failed to parse GUID string: %ls", guidStr.c_str());
    }
    return guid;
}

// Helper methods implementation
IVirtualDesktop* VirtualDesktopAPI::GetDesktopInterface(const GUID& desktopId)
{
    if (!m_pDesktopManagerInternal) {
        return nullptr;
    }

    IVirtualDesktop* pDesktop = nullptr;
    HRESULT hr = m_pDesktopManagerInternal->FindDesktop(const_cast<GUID*>(&desktopId), &pDesktop);

    if (FAILED(hr)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Failed to find desktop interface: 0x%08X", hr);
        return nullptr;
    }

    return pDesktop;
}

std::wstring VirtualDesktopAPI::GetDesktopName(IVirtualDesktop* pDesktop)
{
    if (!pDesktop) {
        return L"Unknown";
    }

    // For now, return a generic name since HSTRING functions are complex
    // In a full implementation, you would need to properly handle Windows Runtime strings
    return L"Desktop";
}

std::wstring VirtualDesktopAPI::GetDesktopWallpaper(IVirtualDesktop* pDesktop)
{
    if (!pDesktop) {
        return L"";
    }

    // For now, return empty string since HSTRING functions are complex
    // In a full implementation, you would need to properly handle Windows Runtime strings
    return L"";
}

} // namespace VirtualDesktop

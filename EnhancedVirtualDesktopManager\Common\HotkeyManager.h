#pragma once

#include <windows.h>
#include <string>
#include <unordered_map>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>

namespace EnhancedVDM {

// Hotkey modifiers
enum class HotkeyModifier : uint32_t {
    None = 0,
    Alt = MOD_ALT,
    Control = MOD_CONTROL,
    Shift = MOD_SHIFT,
    Win = MOD_WIN,
    NoRepeat = MOD_NOREPEAT
};

// Combine modifiers using bitwise OR
inline HotkeyModifier operator|(HotkeyModifier a, HotkeyModifier b) {
    return static_cast<HotkeyModifier>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

inline HotkeyModifier operator&(HotkeyModifier a, HotkeyModifier b) {
    return static_cast<HotkeyModifier>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}

// Hotkey information
struct HotkeyInfo {
    int id;
    HotkeyModifier modifiers;
    UINT virtualKey;
    std::string description;
    std::function<void()> callback;
    bool enabled;
    
    HotkeyInfo() : id(0), modifiers(HotkeyModifier::None), virtualKey(0), enabled(false) {}
    HotkeyInfo(int hotkeyId, HotkeyModifier mods, UINT vk, const std::string& desc, std::function<void()> cb)
        : id(hotkeyId), modifiers(mods), virtualKey(vk), description(desc), callback(cb), enabled(false) {}
};

// Predefined hotkey IDs for common operations
enum class PredefinedHotkeys : int {
    CreateDesktop = 1000,
    SwitchDesktopNext = 1001,
    SwitchDesktopPrev = 1002,
    SwitchDesktop1 = 1003,
    SwitchDesktop2 = 1004,
    SwitchDesktop3 = 1005,
    SwitchDesktop4 = 1006,
    SwitchDesktop5 = 1007,
    SwitchDesktop6 = 1008,
    SwitchDesktop7 = 1009,
    SwitchDesktop8 = 1010,
    SwitchDesktop9 = 1011,
    RemoveCurrentDesktop = 1012,
    MoveWindowToNextDesktop = 1013,
    MoveWindowToPrevDesktop = 1014,
    ShowDesktopList = 1015,
    LaunchIsolatedProcess = 1016,
    ToggleDesktopPreview = 1017,
    QuickSwitchMode = 1018,
    EmergencyDesktopSwitch = 1019,
    CustomHotkeyStart = 2000  // Start of custom hotkey range
};

// Global hotkey manager
class HotkeyManager {
public:
    HotkeyManager();
    ~HotkeyManager();

    // Lifecycle management
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }

    // Hotkey registration
    bool RegisterHotkey(int id, HotkeyModifier modifiers, UINT virtualKey, 
                       const std::string& description, std::function<void()> callback);
    bool RegisterHotkey(PredefinedHotkeys predefinedId, HotkeyModifier modifiers, UINT virtualKey, 
                       std::function<void()> callback);
    bool UnregisterHotkey(int id);
    bool UnregisterHotkey(PredefinedHotkeys predefinedId);
    void UnregisterAllHotkeys();

    // Hotkey management
    bool EnableHotkey(int id);
    bool DisableHotkey(int id);
    bool IsHotkeyEnabled(int id) const;
    bool IsHotkeyRegistered(int id) const;

    // Hotkey information
    std::vector<HotkeyInfo> GetRegisteredHotkeys() const;
    HotkeyInfo GetHotkeyInfo(int id) const;
    std::string GetHotkeyDescription(HotkeyModifier modifiers, UINT virtualKey) const;

    // Default hotkey setup
    void RegisterDefaultHotkeys();
    void SetVirtualDesktopAPI(class VirtualDesktopAPI* api) { m_desktopAPI = api; }

    // Hotkey string parsing
    static bool ParseHotkeyString(const std::string& hotkeyStr, HotkeyModifier& modifiers, UINT& virtualKey);
    static std::string FormatHotkeyString(HotkeyModifier modifiers, UINT virtualKey);
    static std::string GetVirtualKeyName(UINT virtualKey);

    // Configuration
    bool LoadHotkeysFromConfig(const std::string& configFile);
    bool SaveHotkeysToConfig(const std::string& configFile) const;

private:
    bool m_initialized;
    HWND m_messageWindow;
    std::thread m_messageThread;
    std::atomic<bool> m_running;
    std::unordered_map<int, HotkeyInfo> m_hotkeys;
    mutable std::mutex m_hotkeysMutex;
    int m_nextCustomId;
    class VirtualDesktopAPI* m_desktopAPI;

    // Message handling
    void MessageLoop();
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    void HandleHotkeyMessage(int hotkeyId);

    // Helper methods
    bool RegisterSystemHotkey(int id, HotkeyModifier modifiers, UINT virtualKey);
    bool UnregisterSystemHotkey(int id);
    int GetNextCustomId() { return m_nextCustomId++; }

    // Default hotkey callbacks
    void OnCreateDesktop();
    void OnSwitchDesktopNext();
    void OnSwitchDesktopPrev();
    void OnSwitchDesktopByIndex(int index);
    void OnRemoveCurrentDesktop();
    void OnMoveWindowToNextDesktop();
    void OnMoveWindowToPrevDesktop();
    void OnShowDesktopList();
    void OnLaunchIsolatedProcess();
    void OnToggleDesktopPreview();
    void OnQuickSwitchMode();
    void OnEmergencyDesktopSwitch();
};

// Hotkey configuration structure for serialization
struct HotkeyConfig {
    int id;
    uint32_t modifiers;
    uint32_t virtualKey;
    std::string description;
    bool enabled;
};

// Utility functions for hotkey management
namespace HotkeyUtils {
    // Virtual key code mappings
    std::unordered_map<std::string, UINT> GetVirtualKeyMap();
    std::unordered_map<UINT, std::string> GetVirtualKeyNameMap();
    
    // Modifier string parsing
    HotkeyModifier ParseModifierString(const std::string& modStr);
    std::string FormatModifierString(HotkeyModifier modifiers);
    
    // System hotkey validation
    bool IsSystemHotkey(HotkeyModifier modifiers, UINT virtualKey);
    bool IsValidHotkeyCombo(HotkeyModifier modifiers, UINT virtualKey);
    
    // Configuration file helpers
    bool SaveHotkeyConfig(const std::string& filename, const std::vector<HotkeyConfig>& configs);
    bool LoadHotkeyConfig(const std::string& filename, std::vector<HotkeyConfig>& configs);
}

} // namespace EnhancedVDM

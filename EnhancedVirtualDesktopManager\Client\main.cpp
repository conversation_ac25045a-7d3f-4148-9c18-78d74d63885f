#include <windows.h>
#include <iostream>
#include <string>
#include <memory>
#include "ClientApplication.h"
#include "SimpleLogger.h"

using namespace EnhancedVDM;

// Global client instance
ClientApplication* g_pClientApp = nullptr;

// Console control handler for graceful shutdown
BOOL WINAPI ConsoleCtrlHandler(DWORD dwCtrlType)
{
    switch (dwCtrlType) {
    case CTRL_C_EVENT:
    case CTRL_BREAK_EVENT:
    case CTRL_CLOSE_EVENT:
    case CTRL_LOGOFF_EVENT:
    case CTRL_SHUTDOWN_EVENT:
        if (g_pClientApp) {
            std::wcout << L"\nShutting down client...\n";
            g_pClientApp->Shutdown();
        }
        return TRUE;
    default:
        return FALSE;
    }
}

// Show usage information
void ShowUsage(const char* programName)
{
    std::wcout << L"Enhanced Virtual Desktop Manager - Client\n";
    std::wcout << L"========================================\n\n";
    std::wcout << L"Usage: " << std::wstring(programName, programName + strlen(programName)) << L" [options]\n\n";
    std::wcout << L"Options:\n";
    std::wcout << L"  --server <address>     Server address (default: 127.0.0.1)\n";
    std::wcout << L"  --port <port>          Server port (default: 8888)\n";
    std::wcout << L"  --username <user>      Username for authentication\n";
    std::wcout << L"  --password <pass>      Password for authentication\n";
    std::wcout << L"  --name <name>          Client name (default: auto-generated)\n";
    std::wcout << L"  --config <file>        Configuration file (default: client.config)\n";
    std::wcout << L"  --service              Run as Windows service\n";
    std::wcout << L"  --install-service      Install as Windows service\n";
    std::wcout << L"  --uninstall-service    Uninstall Windows service\n";
    std::wcout << L"  --start-service        Start Windows service\n";
    std::wcout << L"  --stop-service         Stop Windows service\n";
    std::wcout << L"  --console              Force console mode (no GUI)\n";
    std::wcout << L"  --no-hotkeys           Disable global hotkeys\n";
    std::wcout << L"  --no-isolation         Disable process isolation\n";
    std::wcout << L"  --verbose              Enable verbose logging\n";
    std::wcout << L"  --help, -h             Show this help message\n";
    std::wcout << L"  --version, -v          Show version information\n\n";
    std::wcout << L"Examples:\n";
    std::wcout << L"  " << std::wstring(programName, programName + strlen(programName)) << L" --server 192.168.1.100 --username admin\n";
    std::wcout << L"  " << std::wstring(programName, programName + strlen(programName)) << L" --install-service\n";
    std::wcout << L"  " << std::wstring(programName, programName + strlen(programName)) << L" --service\n\n";
}

// Show version information
void ShowVersion()
{
    std::wcout << L"Enhanced Virtual Desktop Manager Client v1.0.0\n";
    std::wcout << L"Built for Windows 10/11 Virtual Desktop Remote Control\n";
    std::wcout << L"Copyright (c) 2025 - Modern C++20 Implementation\n\n";
    std::wcout << L"Features:\n";
    std::wcout << L"  - Remote virtual desktop management\n";
    std::wcout << L"  - Program isolation and background execution\n";
    std::wcout << L"  - Global hotkey support\n";
    std::wcout << L"  - Windows service integration\n";
    std::wcout << L"  - Secure network communication\n";
    std::wcout << L"  - Process and window management\n\n";
    std::wcout << L"System Requirements:\n";
    std::wcout << L"  - Windows 10 version 1803 (build 17134) or later\n";
    std::wcout << L"  - Windows 11 (all versions)\n";
    std::wcout << L"  - Network connectivity to server\n\n";
}

// Parse command line arguments
bool ParseCommandLine(int argc, char* argv[], ClientConfig& config, bool& showHelp, bool& showVersion,
                     bool& installService, bool& uninstallService, bool& startService, bool& stopService,
                     bool& runAsService, bool& consoleMode, std::string& configFile)
{
    showHelp = false;
    showVersion = false;
    installService = false;
    uninstallService = false;
    startService = false;
    stopService = false;
    runAsService = false;
    consoleMode = false;
    configFile = "client.config";

    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "--help" || arg == "-h") {
            showHelp = true;
        }
        else if (arg == "--version" || arg == "-v") {
            showVersion = true;
        }
        else if (arg == "--server" && i + 1 < argc) {
            config.serverAddress = argv[++i];
        }
        else if (arg == "--port" && i + 1 < argc) {
            config.serverPort = std::stoi(argv[++i]);
        }
        else if (arg == "--username" && i + 1 < argc) {
            config.username = argv[++i];
        }
        else if (arg == "--password" && i + 1 < argc) {
            config.password = argv[++i];
        }
        else if (arg == "--name" && i + 1 < argc) {
            config.clientName = argv[++i];
        }
        else if (arg == "--config" && i + 1 < argc) {
            configFile = argv[++i];
        }
        else if (arg == "--service") {
            runAsService = true;
        }
        else if (arg == "--install-service") {
            installService = true;
        }
        else if (arg == "--uninstall-service") {
            uninstallService = true;
        }
        else if (arg == "--start-service") {
            startService = true;
        }
        else if (arg == "--stop-service") {
            stopService = true;
        }
        else if (arg == "--console") {
            consoleMode = true;
        }
        else if (arg == "--no-hotkeys") {
            config.enableHotkeys = false;
        }
        else if (arg == "--no-isolation") {
            config.enableProcessIsolation = false;
        }
        else if (arg == "--verbose") {
            // Enable verbose logging
        }
        else {
            std::wcout << L"Unknown argument: " << std::wstring(arg.begin(), arg.end()) << L"\n";
            return false;
        }
    }

    return true;
}

// Main entry point
int main(int argc, char* argv[])
{
    // Set console to handle Unicode properly
    _setmode(_fileno(stdout), _O_U16TEXT);
    _setmode(_fileno(stderr), _O_U16TEXT);
    _setmode(_fileno(stdin), _O_U16TEXT);

    // Initialize COM
    HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
    if (FAILED(hr)) {
        std::wcout << L"Error: Failed to initialize COM\n";
        return 1;
    }

    int exitCode = 0;

    try {
        // Parse command line arguments
        ClientConfig config;
        bool showHelp, showVersion, installService, uninstallService;
        bool startService, stopService, runAsService, consoleMode;
        std::string configFile;

        if (!ParseCommandLine(argc, argv, config, showHelp, showVersion,
                             installService, uninstallService, startService, stopService,
                             runAsService, consoleMode, configFile)) {
            ShowUsage(argv[0]);
            CoUninitialize();
            return 1;
        }

        // Handle help and version requests
        if (showHelp) {
            ShowUsage(argv[0]);
            CoUninitialize();
            return 0;
        }

        if (showVersion) {
            ShowVersion();
            CoUninitialize();
            return 0;
        }

        // Create and initialize the client application
        auto clientApp = std::make_unique<ClientApplication>();
        g_pClientApp = clientApp.get();

        // Set configuration
        clientApp->SetConfiguration(config);

        // Load configuration from file if it exists
        if (!configFile.empty()) {
            clientApp->LoadConfiguration(configFile);
        }

        // Handle service management commands
        if (installService) {
            std::wcout << L"Installing Enhanced Virtual Desktop Manager Client service...\n";
            if (clientApp->InstallService()) {
                std::wcout << L"Service installed successfully\n";
            } else {
                std::wcout << L"Failed to install service\n";
                exitCode = 1;
            }
            CoUninitialize();
            return exitCode;
        }

        if (uninstallService) {
            std::wcout << L"Uninstalling Enhanced Virtual Desktop Manager Client service...\n";
            if (clientApp->UninstallService()) {
                std::wcout << L"Service uninstalled successfully\n";
            } else {
                std::wcout << L"Failed to uninstall service\n";
                exitCode = 1;
            }
            CoUninitialize();
            return exitCode;
        }

        if (startService) {
            std::wcout << L"Starting Enhanced Virtual Desktop Manager Client service...\n";
            if (clientApp->StartService()) {
                std::wcout << L"Service started successfully\n";
            } else {
                std::wcout << L"Failed to start service\n";
                exitCode = 1;
            }
            CoUninitialize();
            return exitCode;
        }

        if (stopService) {
            std::wcout << L"Stopping Enhanced Virtual Desktop Manager Client service...\n";
            if (clientApp->StopService()) {
                std::wcout << L"Service stopped successfully\n";
            } else {
                std::wcout << L"Failed to stop service\n";
                exitCode = 1;
            }
            CoUninitialize();
            return exitCode;
        }

        // Set up console control handler for graceful shutdown
        if (!SetConsoleCtrlHandler(ConsoleCtrlHandler, TRUE)) {
            std::wcout << L"Warning: Could not set console control handler\n";
        }

        // Initialize the client application
        if (!clientApp->Initialize()) {
            std::wcout << L"Error: Failed to initialize client application\n";
            exitCode = 1;
        } else {
            std::wcout << L"Enhanced Virtual Desktop Manager Client v1.0.0\n";
            std::wcout << L"================================================\n\n";
            
            if (runAsService) {
                std::wcout << L"Running as Windows service...\n";
            } else {
                std::wcout << L"Running in console mode...\n";
                std::wcout << L"Server: " << std::wstring(config.serverAddress.begin(), config.serverAddress.end()) 
                          << L":" << config.serverPort << L"\n";
                std::wcout << L"Client: " << std::wstring(config.clientName.begin(), config.clientName.end()) << L"\n\n";
                std::wcout << L"Press Ctrl+C to exit\n\n";
            }

            // Run the application
            exitCode = clientApp->Run();
        }

        // Cleanup
        clientApp->Shutdown();
        g_pClientApp = nullptr;
    }
    catch (const std::exception& e) {
        std::wcout << L"Fatal error: " << std::wstring(e.what(), e.what() + strlen(e.what())) << L"\n";
        exitCode = 1;
    }
    catch (...) {
        std::wcout << L"Fatal error: Unknown exception occurred\n";
        exitCode = 1;
    }

    // Cleanup COM
    CoUninitialize();
    
    return exitCode;
}

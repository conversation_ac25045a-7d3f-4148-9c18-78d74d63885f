@echo off
setlocal enabledelayedexpansion

echo ========================================================================
echo Enhanced Virtual Desktop Manager - Build Script
echo ========================================================================
echo.

:: Set build configuration
set CONFIGURATION=Release
set PLATFORM=x64
set SOLUTION_FILE=EnhancedVirtualDesktopManager.sln

:: Parse command line arguments
:parse_args
if "%1"=="" goto :start_build
if /i "%1"=="debug" (
    set CONFIGURATION=Debug
    shift
    goto :parse_args
)
if /i "%1"=="release" (
    set CONFIGURATION=Release
    shift
    goto :parse_args
)
if /i "%1"=="x86" (
    set PLATFORM=Win32
    shift
    goto :parse_args
)
if /i "%1"=="x64" (
    set PLATFORM=x64
    shift
    goto :parse_args
)
if /i "%1"=="clean" (
    set CLEAN_BUILD=1
    shift
    goto :parse_args
)
if /i "%1"=="help" (
    goto :show_help
)
shift
goto :parse_args

:start_build
echo Configuration: %CONFIGURATION%
echo Platform: %PLATFORM%
echo Solution: %SOLUTION_FILE%
echo.

:: Find MSBuild
set MSBUILD_PATH=""
for /f "usebackq tokens=*" %%i in (`"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -latest -products * -requires Microsoft.Component.MSBuild -property installationPath`) do (
    set VS_PATH=%%i
)

if exist "%VS_PATH%\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%VS_PATH%\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo Error: MSBuild not found. Please install Visual Studio 2022.
    goto :error
)

echo Found MSBuild: %MSBUILD_PATH%
echo.

:: Clean build if requested
if defined CLEAN_BUILD (
    echo Cleaning previous build...
    %MSBUILD_PATH% %SOLUTION_FILE% /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /t:Clean /v:minimal
    if errorlevel 1 goto :error
    
    echo Removing build directories...
    if exist "bin" rmdir /s /q "bin"
    if exist "obj" rmdir /s /q "obj"
    if exist "lib" rmdir /s /q "lib"
    echo.
)

:: Create output directories
if not exist "bin" mkdir "bin"
if not exist "bin\%CONFIGURATION%" mkdir "bin\%CONFIGURATION%"
if not exist "bin\%CONFIGURATION%\%PLATFORM%" mkdir "bin\%CONFIGURATION%\%PLATFORM%"
if not exist "lib" mkdir "lib"
if not exist "lib\%CONFIGURATION%" mkdir "lib\%CONFIGURATION%"
if not exist "lib\%CONFIGURATION%\%PLATFORM%" mkdir "lib\%CONFIGURATION%\%PLATFORM%"

:: Build the solution
echo Building Enhanced Virtual Desktop Manager...
echo Command: %MSBUILD_PATH% %SOLUTION_FILE% /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /v:minimal
echo.

%MSBUILD_PATH% %SOLUTION_FILE% /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /v:minimal

if errorlevel 1 goto :error

echo.
echo ========================================================================
echo Build completed successfully!
echo ========================================================================
echo.

:: Show build output
echo Build Output:
echo --------------
if exist "bin\%CONFIGURATION%\%PLATFORM%\Server.exe" (
    echo [✓] Server.exe - %~z1 bytes
    for %%F in ("bin\%CONFIGURATION%\%PLATFORM%\Server.exe") do echo     Size: %%~zF bytes
    for %%F in ("bin\%CONFIGURATION%\%PLATFORM%\Server.exe") do echo     Date: %%~tF
) else (
    echo [✗] Server.exe - NOT FOUND
)

if exist "bin\%CONFIGURATION%\%PLATFORM%\Client.exe" (
    echo [✓] Client.exe
    for %%F in ("bin\%CONFIGURATION%\%PLATFORM%\Client.exe") do echo     Size: %%~zF bytes
    for %%F in ("bin\%CONFIGURATION%\%PLATFORM%\Client.exe") do echo     Date: %%~tF
) else (
    echo [✗] Client.exe - NOT FOUND
)

if exist "lib\%CONFIGURATION%\%PLATFORM%\Common.lib" (
    echo [✓] Common.lib
    for %%F in ("lib\%CONFIGURATION%\%PLATFORM%\Common.lib") do echo     Size: %%~zF bytes
    for %%F in ("lib\%CONFIGURATION%\%PLATFORM%\Common.lib") do echo     Date: %%~tF
) else (
    echo [✗] Common.lib - NOT FOUND
)

echo.
echo Output Directory: bin\%CONFIGURATION%\%PLATFORM%\
echo Library Directory: lib\%CONFIGURATION%\%PLATFORM%\
echo.

:: Test the executables
echo Testing executables...
echo ----------------------
if exist "bin\%CONFIGURATION%\%PLATFORM%\Server.exe" (
    echo Testing Server.exe --version:
    "bin\%CONFIGURATION%\%PLATFORM%\Server.exe" --version 2>nul
    if errorlevel 1 (
        echo [!] Server.exe may have dependency issues
    ) else (
        echo [✓] Server.exe appears to be working
    )
)

if exist "bin\%CONFIGURATION%\%PLATFORM%\Client.exe" (
    echo Testing Client.exe --version:
    "bin\%CONFIGURATION%\%PLATFORM%\Client.exe" --version 2>nul
    if errorlevel 1 (
        echo [!] Client.exe may have dependency issues
    ) else (
        echo [✓] Client.exe appears to be working
    )
)

echo.
echo ========================================================================
echo Build Summary
echo ========================================================================
echo Configuration: %CONFIGURATION%
echo Platform: %PLATFORM%
echo Build Status: SUCCESS
echo Output Path: %CD%\bin\%CONFIGURATION%\%PLATFORM%\
echo.
echo Next Steps:
echo 1. Run Server.exe to start the control interface
echo 2. Run Client.exe on target machines to connect
echo 3. Configure authentication and network settings as needed
echo.
echo For help: build.bat help
echo ========================================================================

goto :end

:show_help
echo Enhanced Virtual Desktop Manager - Build Script Help
echo ====================================================
echo.
echo Usage: build.bat [options]
echo.
echo Options:
echo   debug          Build in Debug configuration
echo   release        Build in Release configuration (default)
echo   x86            Build for x86 platform
echo   x64            Build for x64 platform (default)
echo   clean          Clean build (remove previous build files)
echo   help           Show this help message
echo.
echo Examples:
echo   build.bat                    # Build Release x64
echo   build.bat debug x86          # Build Debug x86
echo   build.bat clean release      # Clean and build Release x64
echo.
echo Build Requirements:
echo   - Visual Studio 2022 (Community, Professional, or Enterprise)
echo   - Windows SDK 10.0.26100.0 or later
echo   - MSBuild tools
echo.
goto :end

:error
echo.
echo ========================================================================
echo BUILD FAILED!
echo ========================================================================
echo.
echo Please check the error messages above and ensure:
echo 1. Visual Studio 2022 is properly installed
echo 2. Windows SDK 10.0.26100.0 or later is available
echo 3. All source files are present and not corrupted
echo 4. No antivirus software is blocking the build process
echo.
echo For help: build.bat help
echo ========================================================================
exit /b 1

:end
endlocal

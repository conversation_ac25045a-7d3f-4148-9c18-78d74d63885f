#pragma once

#include <windows.h>
#include <shobjidl_core.h>
#include <comdef.h>
#include <winstring.h>
#include <roapi.h>
#include <vector>
#include <string>
#include <memory>

namespace VirtualDesktop {

// Forward declarations
struct IVirtualDesktop;
struct IVirtualDesktopManagerInternal;
struct IVirtualDesktopNotificationService;
struct IVirtualDesktopNotification;

// GUIDs for undocumented interfaces (Windows 10/11)
// These may change between Windows versions
static const GUID CLSID_VirtualDesktopManagerInternal = 
    { 0xC5E0CDCA, 0x7B6E, 0x41B2, { 0x9F, 0xC4, 0xD9, 0x39, 0x75, 0xCC, 0x46, 0x7B } };

static const GUID IID_IVirtualDesktopManagerInternal = 
    { 0xF31574D6, 0xB682, 0x4CDC, { 0xBD, 0x56, 0x18, 0x27, 0x86, 0x0A, 0xBE, 0xC6 } };

static const GUID IID_IVirtualDesktop = 
    { 0xFF72FFDD, 0xBE7E, 0x43FC, { 0x9C, 0x03, 0xAD, 0x81, 0x68, 0x1E, 0x88, 0xE4 } };

// Undocumented IVirtualDesktop interface
struct __declspec(uuid("FF72FFDD-BE7E-43FC-9C03-AD81681E88E4")) IVirtualDesktop : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsViewVisible(IUnknown* pView, int* pfVisible) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetID(GUID* pGuid) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetName(void** name) = 0;  // Simplified for now
    virtual HRESULT STDMETHODCALLTYPE GetWallpaperPath(void** path) = 0;  // Simplified for now
};

// Undocumented IVirtualDesktopManagerInternal interface
struct __declspec(uuid("F31574D6-B682-4CDC-BD56-1827860ABEC6")) IVirtualDesktopManagerInternal : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(UINT* pCount) = 0;
    virtual HRESULT STDMETHODCALLTYPE MoveViewToDesktop(IUnknown* pView, IVirtualDesktop* desktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE CanViewMoveDesktops(IUnknown* pView, int* pfCanViewMoveDesktops) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetCurrentDesktop(IVirtualDesktop** desktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetDesktops(IObjectArray** ppDesktops) = 0;
    virtual HRESULT STDMETHODCALLTYPE GetAdjacentDesktop(IVirtualDesktop* pDesktopReference, int uDirection, IVirtualDesktop** ppAdjacentDesktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE SwitchDesktop(IVirtualDesktop* desktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE CreateDesktopW(IVirtualDesktop** ppNewDesktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE RemoveDesktop(IVirtualDesktop* pRemove, IVirtualDesktop* pFallbackDesktop) = 0;
    virtual HRESULT STDMETHODCALLTYPE FindDesktop(GUID* desktopId, IVirtualDesktop** ppDesktop) = 0;
};

// Desktop information structure
struct DesktopInfo {
    GUID id;
    std::wstring name;
    std::wstring wallpaperPath;
    bool isCurrent;
    int index;
};

// Main Virtual Desktop API wrapper class
class VirtualDesktopAPI {
public:
    VirtualDesktopAPI();
    ~VirtualDesktopAPI();

    // Initialization and cleanup
    bool Initialize();
    void Cleanup();
    bool IsInitialized() const { return m_initialized; }

    // Desktop enumeration and information
    std::vector<DesktopInfo> GetAllDesktops();
    DesktopInfo GetCurrentDesktop();
    int GetDesktopCount();
    DesktopInfo GetDesktopByIndex(int index);
    DesktopInfo GetDesktopById(const GUID& id);

    // Desktop management
    bool CreateDesktop(DesktopInfo& newDesktop);
    bool RemoveDesktop(const GUID& desktopId);
    bool SwitchToDesktop(const GUID& desktopId);
    bool SwitchToDesktopByIndex(int index);

    // Window management
    bool MoveWindowToDesktop(HWND hwnd, const GUID& desktopId);
    bool IsWindowOnCurrentDesktop(HWND hwnd);
    GUID GetWindowDesktopId(HWND hwnd);

    // Utility functions
    std::wstring GuidToString(const GUID& guid);
    GUID StringToGuid(const std::wstring& guidStr);

private:
    bool m_initialized;
    IVirtualDesktopManager* m_pDesktopManager;
    IVirtualDesktopManagerInternal* m_pDesktopManagerInternal;

    // Helper methods
    bool InitializeCOM();
    void CleanupCOM();
    IVirtualDesktop* GetDesktopInterface(const GUID& desktopId);
    std::wstring GetDesktopName(IVirtualDesktop* pDesktop);
    std::wstring GetDesktopWallpaper(IVirtualDesktop* pDesktop);
};

} // namespace VirtualDesktop

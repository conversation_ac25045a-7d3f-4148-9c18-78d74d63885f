#pragma once

#include <windows.h>
#include <commctrl.h>
#include <memory>
#include <string>
#include <vector>
#include "NetworkProtocol.h"
#include "SecurityManager.h"
#include "SimpleLogger.h"

namespace EnhancedVDM {

// Forward declarations
class MainWindow;
class ClientManager;
class CommandProcessor;

// Server configuration
struct ServerConfig {
    int port;
    SecurityLevel securityLevel;
    bool autoStart;
    bool minimizeToTray;
    bool logToFile;
    std::string logFilePath;
    int maxClients;
    int sessionTimeoutMinutes;
    bool requireAuthentication;
    
    ServerConfig() 
        : port(DEFAULT_PORT), securityLevel(SecurityLevel::Standard), 
          autoStart(false), minimizeToTray(true), logToFile(true),
          logFilePath("server.log"), maxClients(MAX_CLIENTS), 
          sessionTimeoutMinutes(30), requireAuthentication(true) {}
};

// Server statistics
struct ServerStats {
    std::chrono::steady_clock::time_point startTime;
    size_t totalConnections;
    size_t currentConnections;
    size_t totalCommandsProcessed;
    size_t totalBytesReceived;
    size_t totalBytesSent;
    size_t authenticationFailures;
    std::chrono::steady_clock::time_point lastActivity;
    
    ServerStats() : totalConnections(0), currentConnections(0), 
                   totalCommandsProcessed(0), totalBytesReceived(0), 
                   totalBytesSent(0), authenticationFailures(0) {
        startTime = std::chrono::steady_clock::now();
        lastActivity = startTime;
    }
};

// Main server application class
class ServerApplication {
public:
    ServerApplication();
    ~ServerApplication();

    // Application lifecycle
    bool Initialize(HINSTANCE hInstance);
    int Run();
    void Shutdown();

    // Server control
    bool StartServer();
    bool StopServer();
    bool IsServerRunning() const { return m_serverRunning; }

    // Configuration
    bool LoadConfiguration(const std::string& configFile = "server.config");
    bool SaveConfiguration(const std::string& configFile = "server.config") const;
    const ServerConfig& GetConfiguration() const { return m_config; }
    void SetConfiguration(const ServerConfig& config) { m_config = config; }

    // Statistics and monitoring
    const ServerStats& GetStatistics() const { return m_stats; }
    void UpdateStatistics();
    std::vector<std::string> GetRecentActivity(int maxEntries = 50) const;

    // Client management
    std::vector<std::shared_ptr<ClientInfo>> GetConnectedClients() const;
    bool DisconnectClient(const std::string& sessionToken);
    void BroadcastMessage(const std::string& message);

    // Event handling
    void OnClientConnected(std::shared_ptr<ClientInfo> client);
    void OnClientDisconnected(std::shared_ptr<ClientInfo> client);
    void OnMessageReceived(std::shared_ptr<ClientInfo> client, MessageType type, 
                          const void* data, size_t dataSize);

    // UI integration
    void SetMainWindow(MainWindow* mainWindow) { m_mainWindow = mainWindow; }
    void ShowNotification(const std::string& title, const std::string& message);
    void UpdateUI();

    // Logging
    void LogMessage(const std::string& message, LogLevel level = LogLevel::Info);
    void LogError(const std::string& error);
    void LogSecurity(const std::string& event);

    // System tray integration
    bool CreateSystemTrayIcon();
    void RemoveSystemTrayIcon();
    void ShowContextMenu(POINT pt);
    void OnSystemTrayMessage(UINT message, WPARAM wParam, LPARAM lParam);

private:
    HINSTANCE m_hInstance;
    MainWindow* m_mainWindow;
    std::unique_ptr<NetworkServer> m_networkServer;
    std::unique_ptr<SecurityManager> m_securityManager;
    std::unique_ptr<ClientManager> m_clientManager;
    std::unique_ptr<CommandProcessor> m_commandProcessor;
    
    ServerConfig m_config;
    ServerStats m_stats;
    bool m_initialized;
    bool m_serverRunning;
    
    // System tray
    NOTIFYICONDATA m_trayIcon;
    bool m_trayIconCreated;
    UINT m_trayMessage;
    
    // Recent activity log
    std::vector<std::string> m_recentActivity;
    mutable std::mutex m_activityMutex;
    static constexpr size_t MAX_ACTIVITY_ENTRIES = 1000;

    // Initialization helpers
    bool InitializeComponents();
    bool InitializeNetworking();
    bool InitializeSecurity();
    bool InitializeLogging();
    
    // Configuration helpers
    bool LoadDefaultConfiguration();
    bool ValidateConfiguration() const;
    
    // Activity logging
    void AddActivityEntry(const std::string& entry);
    std::string FormatActivityEntry(const std::string& event, const std::string& details = "") const;
    
    // UI update helpers
    void NotifyUIUpdate();
    void UpdateTrayIcon();
    void UpdateTrayTooltip();
    
    // Message handlers
    void HandleAuthRequest(std::shared_ptr<ClientInfo> client, const void* data, size_t dataSize);
    void HandleDesktopRequest(std::shared_ptr<ClientInfo> client, MessageType type, 
                             const void* data, size_t dataSize);
    void HandleProcessRequest(std::shared_ptr<ClientInfo> client, MessageType type, 
                             const void* data, size_t dataSize);
    void HandleWindowRequest(std::shared_ptr<ClientInfo> client, MessageType type, 
                            const void* data, size_t dataSize);
    void HandleStatusRequest(std::shared_ptr<ClientInfo> client, const void* data, size_t dataSize);
    
    // Error handling
    void HandleNetworkError(const std::string& error);
    void HandleSecurityError(const std::string& error);
    void HandleClientError(std::shared_ptr<ClientInfo> client, const std::string& error);
    
    // Cleanup
    void CleanupComponents();
    void CleanupNetworking();
    void CleanupSecurity();
};

// Global application instance
extern ServerApplication* g_pServerApp;

} // namespace EnhancedVDM

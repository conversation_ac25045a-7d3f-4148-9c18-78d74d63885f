@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Virtual Desktop Manager Build Script
echo ========================================
echo.

:: Check if Visual Studio is available
where msbuild >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: MSBuild not found in PATH
    echo Please run this script from a Visual Studio Developer Command Prompt
    echo or ensure Visual Studio Build Tools are installed and in PATH
    pause
    exit /b 1
)

:: Set default configuration and platform
set CONFIG=Release
set PLATFORM=x64
set SOLUTION=VirtualDesktopManager.sln

:: Parse command line arguments
:parse_args
if "%~1"=="" goto build
if /i "%~1"=="debug" (
    set CONFIG=Debug
    shift
    goto parse_args
)
if /i "%~1"=="release" (
    set CONFIG=Release
    shift
    goto parse_args
)
if /i "%~1"=="x86" (
    set PLATFORM=Win32
    shift
    goto parse_args
)
if /i "%~1"=="x64" (
    set PLATFORM=x64
    shift
    goto parse_args
)
if /i "%~1"=="clean" (
    set CLEAN=1
    shift
    goto parse_args
)
if /i "%~1"=="help" goto show_help
if /i "%~1"=="-h" goto show_help
if /i "%~1"=="--help" goto show_help

echo Warning: Unknown argument "%~1"
shift
goto parse_args

:show_help
echo Usage: build.bat [options]
echo.
echo Options:
echo   debug          Build in Debug configuration (default: Release)
echo   release        Build in Release configuration
echo   x86            Build for x86 platform
echo   x64            Build for x64 platform (default)
echo   clean          Clean before building
echo   help, -h       Show this help message
echo.
echo Examples:
echo   build.bat
echo   build.bat debug x86
echo   build.bat clean release x64
echo.
exit /b 0

:build
echo Configuration: %CONFIG%
echo Platform: %PLATFORM%
echo Solution: %SOLUTION%
echo.

:: Check if solution file exists
if not exist "%SOLUTION%" (
    echo Error: Solution file "%SOLUTION%" not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

:: Clean if requested
if defined CLEAN (
    echo Cleaning solution...
    msbuild "%SOLUTION%" /t:Clean /p:Configuration=%CONFIG% /p:Platform=%PLATFORM% /v:minimal
    if !errorlevel! neq 0 (
        echo Error: Clean failed
        pause
        exit /b 1
    )
    echo Clean completed successfully
    echo.
)

:: Build the solution
echo Building solution...
msbuild "%SOLUTION%" /t:Build /p:Configuration=%CONFIG% /p:Platform=%PLATFORM% /v:minimal /m

if %errorlevel% neq 0 (
    echo.
    echo ========================================
    echo BUILD FAILED
    echo ========================================
    echo.
    echo The build failed with error code %errorlevel%
    echo Please check the output above for error details
    echo.
    echo Common solutions:
    echo - Ensure Windows SDK 10.0.19041.0 or later is installed
    echo - Verify Visual Studio 2019+ with C++ workload is installed
    echo - Check that all source files are present
    echo - Try cleaning the solution first: build.bat clean
    echo.
    pause
    exit /b %errorlevel%
)

:: Check if output file was created
set OUTPUT_DIR=bin\%CONFIG%\%PLATFORM%
set OUTPUT_FILE=%OUTPUT_DIR%\VirtualDesktopManager.exe

if not exist "%OUTPUT_FILE%" (
    echo.
    echo Warning: Expected output file not found: %OUTPUT_FILE%
    echo Build may have succeeded but output location is different
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL
    echo ========================================
    echo.
    echo Output: %OUTPUT_FILE%
    
    :: Get file size and version info
    for %%A in ("%OUTPUT_FILE%") do (
        echo Size: %%~zA bytes
        echo Modified: %%~tA
    )
    
    echo.
    echo You can now run the application:
    echo   %OUTPUT_FILE% help
    echo.
    echo Or test basic functionality:
    echo   %OUTPUT_FILE% list
    echo   %OUTPUT_FILE% count
    echo   %OUTPUT_FILE% current
    echo.
)

:: Create a simple test script
set TEST_SCRIPT=test.bat
echo @echo off > "%TEST_SCRIPT%"
echo echo Testing Virtual Desktop Manager... >> "%TEST_SCRIPT%"
echo echo. >> "%TEST_SCRIPT%"
echo echo Current desktop: >> "%TEST_SCRIPT%"
echo "%OUTPUT_FILE%" current >> "%TEST_SCRIPT%"
echo echo. >> "%TEST_SCRIPT%"
echo echo Desktop count: >> "%TEST_SCRIPT%"
echo "%OUTPUT_FILE%" count >> "%TEST_SCRIPT%"
echo echo. >> "%TEST_SCRIPT%"
echo echo All desktops: >> "%TEST_SCRIPT%"
echo "%OUTPUT_FILE%" list >> "%TEST_SCRIPT%"
echo pause >> "%TEST_SCRIPT%"

echo Test script created: %TEST_SCRIPT%
echo Run this script to test the application functionality
echo.

endlocal
pause

#pragma once

#include <windows.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include "VirtualDesktopAPI.h"

#pragma comment(lib, "psapi.lib")

namespace EnhancedVDM {

// Process launch options
struct ProcessLaunchOptions {
    std::wstring commandLine;
    std::wstring workingDirectory;
    std::wstring windowTitle;
    GUID targetDesktopId;
    bool hideWindow;
    bool isolateProcess;
    bool runAsAdmin;
    bool inheritHandles;
    DWORD creationFlags;
    std::unordered_map<std::wstring, std::wstring> environment;
    
    ProcessLaunchOptions() 
        : hideWindow(false), isolateProcess(true), runAsAdmin(false), 
          inheritHandles(false), creationFlags(0) {
        memset(&targetDesktopId, 0, sizeof(GUID));
    }
};

// Process monitoring information
struct ProcessMonitorInfo {
    DWORD processId;
    HANDLE processHandle;
    HWND mainWindow;
    std::wstring processName;
    std::wstring commandLine;
    std::wstring windowTitle;
    GUID desktopId;
    bool isIsolated;
    bool isHidden;
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point lastActivity;
    DWORD exitCode;
    bool hasExited;
    
    ProcessMonitorInfo() 
        : processId(0), processHandle(nullptr), mainWindow(nullptr), 
          isIsolated(false), isHidden(false), exitCode(0), hasExited(false) {
        memset(&desktopId, 0, sizeof(GUID));
    }
};

// Process event types
enum class ProcessEvent {
    ProcessStarted,
    ProcessExited,
    WindowCreated,
    WindowDestroyed,
    WindowMoved,
    DesktopSwitched
};

// Process manager class
class ProcessManager {
public:
    ProcessManager();
    ~ProcessManager();

    // Lifecycle management
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }

    // Process launching
    bool LaunchProcess(const ProcessLaunchOptions& options, ProcessMonitorInfo& processInfo);
    bool LaunchProcessOnDesktop(const std::wstring& commandLine, const GUID& desktopId, 
                               ProcessMonitorInfo& processInfo);
    bool LaunchIsolatedProcess(const std::wstring& commandLine, const std::wstring& desktopName,
                              ProcessMonitorInfo& processInfo);

    // Process management
    bool TerminateProcess(DWORD processId, bool force = false);
    bool SuspendProcess(DWORD processId);
    bool ResumeProcess(DWORD processId);
    bool MoveProcessToDesktop(DWORD processId, const GUID& desktopId);

    // Process monitoring
    std::vector<ProcessMonitorInfo> GetManagedProcesses() const;
    std::vector<ProcessMonitorInfo> GetProcessesOnDesktop(const GUID& desktopId) const;
    ProcessMonitorInfo GetProcessInfo(DWORD processId) const;
    bool IsProcessManaged(DWORD processId) const;

    // Window management
    std::vector<HWND> GetProcessWindows(DWORD processId) const;
    bool MoveWindowToDesktop(HWND hwnd, const GUID& desktopId);
    bool HideWindow(HWND hwnd);
    bool ShowWindow(HWND hwnd);

    // Desktop isolation
    bool CreateIsolatedDesktop(const std::wstring& desktopName, GUID& desktopId);
    bool RemoveIsolatedDesktop(const GUID& desktopId);
    std::vector<GUID> GetIsolatedDesktops() const;

    // Process enumeration
    std::vector<DWORD> EnumerateAllProcesses() const;
    std::vector<HWND> EnumerateAllWindows() const;
    std::vector<HWND> EnumerateWindowsOnDesktop(const GUID& desktopId) const;

    // Event handling
    using ProcessEventHandler = std::function<void(ProcessEvent, const ProcessMonitorInfo&)>;
    void SetProcessEventHandler(ProcessEventHandler handler) { m_eventHandler = handler; }

    // Configuration
    void SetVirtualDesktopAPI(VirtualDesktopAPI* api) { m_desktopAPI = api; }
    void SetAutoCleanup(bool enabled) { m_autoCleanup = enabled; }
    void SetMonitoringInterval(DWORD intervalMs) { m_monitoringInterval = intervalMs; }

    // Statistics
    size_t GetManagedProcessCount() const;
    size_t GetIsolatedProcessCount() const;
    std::chrono::steady_clock::duration GetTotalUptime() const;

private:
    bool m_initialized;
    VirtualDesktopAPI* m_desktopAPI;
    std::unordered_map<DWORD, std::shared_ptr<ProcessMonitorInfo>> m_managedProcesses;
    mutable std::mutex m_processesMutex;
    std::thread m_monitoringThread;
    std::atomic<bool> m_running;
    DWORD m_monitoringInterval;
    bool m_autoCleanup;
    ProcessEventHandler m_eventHandler;
    std::chrono::steady_clock::time_point m_startTime;

    // Monitoring and cleanup
    void MonitoringLoop();
    void CleanupExitedProcesses();
    void UpdateProcessInfo(std::shared_ptr<ProcessMonitorInfo> processInfo);

    // Process creation helpers
    bool CreateProcessWithDesktop(const ProcessLaunchOptions& options, PROCESS_INFORMATION& pi);
    bool SetProcessDesktop(DWORD processId, const GUID& desktopId);
    HWND FindMainWindow(DWORD processId);
    std::wstring GetProcessCommandLine(DWORD processId);
    std::wstring GetProcessName(DWORD processId);

    // Window enumeration callbacks
    static BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam);
    static BOOL CALLBACK EnumProcessWindowsProc(HWND hwnd, LPARAM lParam);

    struct EnumWindowsData {
        std::vector<HWND>* windows;
        DWORD processId;
        GUID* desktopId;
        VirtualDesktopAPI* desktopAPI;
    };

    // Event notification
    void NotifyProcessEvent(ProcessEvent event, const ProcessMonitorInfo& processInfo);

    // Utility methods
    bool IsWindowVisible(HWND hwnd) const;
    bool IsMainWindow(HWND hwnd) const;
    std::wstring GetWindowTitle(HWND hwnd) const;
    std::wstring GetWindowClassName(HWND hwnd) const;
};

// Process utilities
namespace ProcessUtils {
    // Process information
    std::wstring GetProcessName(DWORD processId);
    std::wstring GetProcessPath(DWORD processId);
    std::wstring GetProcessCommandLine(DWORD processId);
    DWORD GetProcessParentId(DWORD processId);
    bool IsProcessRunning(DWORD processId);
    bool IsProcess64Bit(DWORD processId);

    // Window utilities
    DWORD GetWindowProcessId(HWND hwnd);
    std::wstring GetWindowTitle(HWND hwnd);
    std::wstring GetWindowClassName(HWND hwnd);
    bool IsWindowMainWindow(HWND hwnd);
    bool IsWindowVisible(HWND hwnd);
    RECT GetWindowRect(HWND hwnd);

    // Process launching
    bool LaunchProcessAsUser(const std::wstring& commandLine, const std::wstring& workingDir,
                            HANDLE userToken, PROCESS_INFORMATION& pi);
    bool LaunchProcessElevated(const std::wstring& commandLine, const std::wstring& workingDir,
                              PROCESS_INFORMATION& pi);

    // Environment manipulation
    std::vector<wchar_t> CreateEnvironmentBlock(const std::unordered_map<std::wstring, std::wstring>& env);
    std::unordered_map<std::wstring, std::wstring> GetCurrentEnvironment();

    // Security and permissions
    bool EnableDebugPrivilege();
    bool HasProcessAccess(DWORD processId, DWORD desiredAccess);
    HANDLE OpenProcessWithAccess(DWORD processId, DWORD desiredAccess);

    // Process monitoring
    bool WaitForProcessExit(HANDLE processHandle, DWORD timeoutMs = INFINITE);
    DWORD GetProcessExitCode(HANDLE processHandle);
    bool IsProcessResponding(DWORD processId);

    // Desktop utilities
    bool SetProcessWindowStation(DWORD processId, HWINSTA windowStation);
    bool SetProcessDesktop(DWORD processId, HDESK desktop);
    HDESK GetProcessDesktop(DWORD processId);
}

} // namespace EnhancedVDM

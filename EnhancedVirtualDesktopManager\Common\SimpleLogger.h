#pragma once
#include <windows.h>
#include <string>

namespace EnhancedVDM {

enum class LogLevel : int {
    Info = 0,
    Warning = 1,
    Error = 2,
    Debug = 3,
    Network = 4,
    Security = 5
};

class SimpleLogger {
private:
    static bool initialized_;
    static HANDLE log_file_;
    static constexpr size_t MAX_LOG_SIZE = 2048;  // Increased for network logs
    
public:
    static void Initialize(const char* log_path = "enhanced_vdm.log") noexcept;
    static void Log(LogLevel level, const char* format, ...) noexcept;
    static void LogAppStart(const char* app_name, const char* path = nullptr) noexcept;
    static void LogAppEnd(const char* app_name, DWORD exit_code = 0) noexcept;
    static void LogNetwork(const char* format, ...) noexcept;
    static void LogSecurity(const char* format, ...) noexcept;
    static void Shutdown() noexcept;
    
private:
    static const char* GetLogLevelString(LogLevel level) noexcept;
    static std::string GetTimestamp() noexcept;
    static void LogInternal(LogLevel level, const char* format, va_list args) noexcept;
};

} // namespace EnhancedVDM

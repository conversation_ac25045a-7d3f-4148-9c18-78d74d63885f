#pragma once

#include <windows.h>
#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <vector>
#include "NetworkProtocol.h"
#include "VirtualDesktopAPI.h"
#include "VirtualDesktopManager.h"
#include "HotkeyManager.h"
#include "ProcessManager.h"
#include "SimpleLogger.h"

namespace EnhancedVDM {

// Forward declarations
class RemoteDesktopManager;
class CommandHandler;
class ServiceManager;

// Client configuration
struct ClientConfig {
    std::string serverAddress;
    int serverPort;
    std::string username;
    std::string password;
    std::string clientName;
    bool autoConnect;
    bool runAsService;
    bool enableHotkeys;
    bool enableProcessIsolation;
    bool hideFromTaskbar;
    int reconnectInterval;
    bool logToFile;
    std::string logFilePath;
    
    ClientConfig() 
        : serverAddress("127.0.0.1"), serverPort(DEFAULT_PORT), 
          clientName("EnhancedVDM-Client"), autoConnect(false), 
          runAsService(false), enableHotkeys(true), 
          enableProcessIsolation(true), hideFromTaskbar(false),
          reconnectInterval(30), logToFile(true), 
          logFilePath("client.log") {}
};

// Client status
enum class ClientStatus {
    Disconnected,
    Connecting,
    Authenticating,
    Connected,
    Error,
    Reconnecting
};

// Client statistics
struct ClientStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point lastConnection;
    size_t totalConnections;
    size_t totalReconnections;
    size_t totalCommandsReceived;
    size_t totalCommandsExecuted;
    size_t totalBytesReceived;
    size_t totalBytesSent;
    std::chrono::steady_clock::duration totalUptime;
    std::chrono::steady_clock::duration totalConnectedTime;
    
    ClientStats() : totalConnections(0), totalReconnections(0), 
                   totalCommandsReceived(0), totalCommandsExecuted(0),
                   totalBytesReceived(0), totalBytesSent(0) {
        startTime = std::chrono::steady_clock::now();
    }
};

// Main client application class
class ClientApplication {
public:
    ClientApplication();
    ~ClientApplication();

    // Application lifecycle
    bool Initialize();
    int Run();
    void Shutdown();

    // Connection management
    bool Connect();
    void Disconnect();
    bool IsConnected() const { return m_status == ClientStatus::Connected; }
    ClientStatus GetStatus() const { return m_status; }

    // Configuration
    bool LoadConfiguration(const std::string& configFile = "client.config");
    bool SaveConfiguration(const std::string& configFile = "client.config") const;
    const ClientConfig& GetConfiguration() const { return m_config; }
    void SetConfiguration(const ClientConfig& config) { m_config = config; }

    // Statistics and monitoring
    const ClientStats& GetStatistics() const { return m_stats; }
    void UpdateStatistics();

    // Service mode
    bool InstallService();
    bool UninstallService();
    bool StartService();
    bool StopService();
    bool IsServiceInstalled() const;
    bool IsServiceRunning() const;

    // Message handling
    void OnMessageReceived(MessageType type, const void* data, size_t dataSize);
    void OnDisconnected();

    // Command execution
    bool ExecuteCommand(MessageType commandType, const void* data, size_t dataSize);
    void SendResponse(MessageType responseType, const void* data, size_t dataSize);
    void SendStatus();

    // Desktop management integration
    VirtualDesktopManager* GetDesktopManager() { return m_desktopManager.get(); }
    HotkeyManager* GetHotkeyManager() { return m_hotkeyManager.get(); }
    ProcessManager* GetProcessManager() { return m_processManager.get(); }

    // Logging
    void LogMessage(const std::string& message, LogLevel level = LogLevel::Info);
    void LogError(const std::string& error);
    void LogNetwork(const std::string& event);

    // System tray integration (for non-service mode)
    bool CreateSystemTrayIcon();
    void RemoveSystemTrayIcon();
    void ShowContextMenu(POINT pt);
    void OnSystemTrayMessage(UINT message, WPARAM wParam, LPARAM lParam);

private:
    ClientConfig m_config;
    ClientStats m_stats;
    ClientStatus m_status;
    bool m_initialized;
    bool m_running;
    std::atomic<bool> m_shouldReconnect;
    
    // Core components
    std::unique_ptr<NetworkClient> m_networkClient;
    std::unique_ptr<VirtualDesktopManager> m_desktopManager;
    std::unique_ptr<HotkeyManager> m_hotkeyManager;
    std::unique_ptr<ProcessManager> m_processManager;
    std::unique_ptr<RemoteDesktopManager> m_remoteDesktopManager;
    std::unique_ptr<CommandHandler> m_commandHandler;
    std::unique_ptr<ServiceManager> m_serviceManager;
    
    // Threading
    std::thread m_mainThread;
    std::thread m_reconnectThread;
    std::mutex m_statusMutex;
    
    // System tray
    NOTIFYICONDATA m_trayIcon;
    bool m_trayIconCreated;
    UINT m_trayMessage;
    HWND m_messageWindow;
    
    // Initialization
    bool InitializeComponents();
    bool InitializeNetworking();
    bool InitializeDesktopManager();
    bool InitializeHotkeys();
    bool InitializeProcessManager();
    bool InitializeLogging();
    
    // Main loop
    void MainLoop();
    void ReconnectLoop();
    
    // Connection management
    bool AttemptConnection();
    bool AuthenticateWithServer();
    void HandleConnectionLost();
    void SetStatus(ClientStatus status);
    
    // Message processing
    void ProcessMessageQueue();
    void HandleAuthResponse(const void* data, size_t dataSize);
    void HandleDesktopCommand(MessageType type, const void* data, size_t dataSize);
    void HandleProcessCommand(MessageType type, const void* data, size_t dataSize);
    void HandleWindowCommand(MessageType type, const void* data, size_t dataSize);
    void HandleStatusRequest(const void* data, size_t dataSize);
    void HandleHeartbeat(const void* data, size_t dataSize);
    
    // Configuration helpers
    bool LoadDefaultConfiguration();
    bool ValidateConfiguration() const;
    std::string GetComputerName() const;
    std::string GetUserName() const;
    
    // System integration
    bool RegisterForAutoStart();
    bool UnregisterFromAutoStart();
    bool IsRegisteredForAutoStart() const;
    
    // Message window for system tray
    bool CreateMessageWindow();
    void DestroyMessageWindow();
    static LRESULT CALLBACK MessageWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
    // Error handling
    void HandleNetworkError(const std::string& error);
    void HandleDesktopError(const std::string& error);
    void HandleSystemError(const std::string& error);
    
    // Cleanup
    void CleanupComponents();
    void CleanupNetworking();
    void CleanupSystemTray();
    
    // Statistics helpers
    void UpdateConnectionStats();
    void UpdateCommandStats();
    void UpdateNetworkStats(size_t bytesReceived, size_t bytesSent);
};

// Global client instance
extern ClientApplication* g_pClientApp;

} // namespace EnhancedVDM
